<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ПРОВЕРКА ЧТО ЛОГИ ДОБАВЛЯЮТСЯ ===\n";
    
    $admin = User::where('name', 'admin')->first();
    $battleLogService = app(BattleLogService::class);
    
    $baseKey = "battle_logs:{$admin->id}";
    $redisKey = "{$baseKey}:logs";
    
    echo "Игрок: {$admin->name} (ID: {$admin->id})\n";
    echo "Redis ключ: {$redisKey}\n";
    
    // 1. Получаем первый лог до добавления
    echo "\n1️⃣ Первый лог до добавления:\n";
    
    $logsBefore = Redis::lrange($redisKey, 0, 0);
    if (!empty($logsBefore)) {
        $firstLogBefore = json_decode($logsBefore[0], true);
        echo "   Сообщение: " . strip_tags($firstLogBefore['message'] ?? 'неизвестно') . "\n";
        echo "   Время: " . ($firstLogBefore['timestamp'] ?? 'неизвестно') . "\n";
    } else {
        echo "   Логов нет\n";
    }
    
    // 2. Добавляем уникальный лог
    echo "\n2️⃣ Добавление уникального лога:\n";
    
    $uniqueMessage = "ТЕСТ АВТОАТАК РУДНИКОВ " . date('H:i:s') . " - " . rand(1000, 9999);
    echo "   Добавляем: {$uniqueMessage}\n";
    
    $battleLogService->addLog($baseKey, $uniqueMessage, 'mine_test');
    
    // 3. Получаем первый лог после добавления
    echo "\n3️⃣ Первый лог после добавления:\n";
    
    $logsAfter = Redis::lrange($redisKey, 0, 0);
    if (!empty($logsAfter)) {
        $firstLogAfter = json_decode($logsAfter[0], true);
        echo "   Сообщение: " . strip_tags($firstLogAfter['message'] ?? 'неизвестно') . "\n";
        echo "   Время: " . ($firstLogAfter['timestamp'] ?? 'неизвестно') . "\n";
        echo "   Тип: " . ($firstLogAfter['type'] ?? 'неизвестно') . "\n";
    } else {
        echo "   Логов нет\n";
    }
    
    // 4. Проверяем, изменился ли первый лог
    echo "\n4️⃣ Результат:\n";
    
    if (!empty($logsBefore) && !empty($logsAfter)) {
        $messageBefore = strip_tags($firstLogBefore['message'] ?? '');
        $messageAfter = strip_tags($firstLogAfter['message'] ?? '');
        
        if ($messageBefore !== $messageAfter) {
            echo "   ✅ УСПЕХ! Первый лог изменился - новый лог добавлен\n";
            echo "   Было: {$messageBefore}\n";
            echo "   Стало: {$messageAfter}\n";
        } else {
            echo "   ❌ ПРОВАЛ! Первый лог не изменился\n";
        }
    } else {
        echo "   ❓ Не удалось сравнить логи\n";
    }
    
    // 5. Проверяем через getLogs
    echo "\n5️⃣ Проверка через getLogs:\n";
    
    $retrievedLogs = $battleLogService->getLogs($baseKey, null, 3);
    echo "   Первые 3 лога:\n";
    
    foreach ($retrievedLogs as $index => $log) {
        $message = strip_tags($log['message'] ?? 'неизвестно');
        $type = $log['type'] ?? 'неизвестно';
        echo "     " . ($index + 1) . ". [{$type}] {$message}\n";
    }
    
    // Проверяем, есть ли наш тестовый лог
    $foundTestLog = false;
    foreach ($retrievedLogs as $log) {
        if (strpos($log['message'] ?? '', 'ТЕСТ АВТОАТАК РУДНИКОВ') !== false) {
            $foundTestLog = true;
            break;
        }
    }
    
    echo "\n6️⃣ Итоговая проверка:\n";
    if ($foundTestLog) {
        echo "   ✅ ТЕСТОВЫЙ ЛОГ НАЙДЕН! Система журнала боя работает!\n";
    } else {
        echo "   ❌ Тестовый лог не найден\n";
    }
    
    echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
