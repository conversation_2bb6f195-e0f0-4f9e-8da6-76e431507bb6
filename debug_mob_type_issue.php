<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДИАГНОСТИКА ПРОБЛЕМЫ С MOB_TYPE ===\n";

try {
    $user = User::find(7);
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    echo "   Основная локация ID: {$mineLocation->location_id}\n";

    // 1. Проверяем всех мобов в руднике
    echo "\n1️⃣ Проверка всех мобов в руднике...\n";
    $allMobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mine_location_id', $mineLocation->id)
        ->get();
    
    echo "   Всего мобов в руднике: {$allMobs->count()}\n";
    
    foreach ($allMobs as $mob) {
        echo "     - ID: {$mob->id}, Имя: {$mob->name}\n";
        echo "       HP: {$mob->hp}/{$mob->max_hp}\n";
        echo "       mob_type: " . ($mob->mob_type ?? 'NULL') . "\n";
        echo "       location_id: {$mob->location_id}\n";
        echo "       mine_location_id: {$mob->mine_location_id}\n";
        echo "       death_time: " . ($mob->death_time ?? 'NULL') . "\n";
        echo "\n";
    }

    // 2. Проверяем фильтр MineTargetDistributionService
    echo "2️⃣ Проверка фильтра MineTargetDistributionService...\n";
    
    // Запрос как в getAvailableMobs
    $filteredMobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->where(function ($query) {
            $query->whereNull('death_time')
                ->orWhere('death_time', '<', now()->subMinutes(5));
        })
        ->get();
    
    echo "   Мобов после фильтра mob_type='mine': {$filteredMobs->count()}\n";
    
    foreach ($filteredMobs as $mob) {
        echo "     - {$mob->name} (ID: {$mob->id})\n";
    }

    // 3. Проверяем без фильтра mob_type
    echo "\n3️⃣ Проверка без фильтра mob_type...\n";
    
    $mobsWithoutTypeFilter = Mob::where('location_id', $mineLocation->location_id)
        ->where('hp', '>', 0)
        ->where(function ($query) {
            $query->whereNull('death_time')
                ->orWhere('death_time', '<', now()->subMinutes(5));
        })
        ->get();
    
    echo "   Мобов без фильтра mob_type: {$mobsWithoutTypeFilter->count()}\n";
    
    foreach ($mobsWithoutTypeFilter as $mob) {
        echo "     - {$mob->name} (ID: {$mob->id}), mob_type: " . ($mob->mob_type ?? 'NULL') . "\n";
    }

    // 4. Исправляем mob_type для тестового моба
    echo "\n4️⃣ Исправление mob_type для тестового моба...\n";
    
    $testMob = Mob::where('name', 'Тестовый рудничный моб')->first();
    if ($testMob) {
        echo "   Найден тестовый моб: {$testMob->name} (ID: {$testMob->id})\n";
        echo "   Текущий mob_type: " . ($testMob->mob_type ?? 'NULL') . "\n";
        
        if ($testMob->mob_type !== 'mine') {
            $testMob->mob_type = 'mine';
            $testMob->save();
            echo "   ✅ mob_type изменен на 'mine'\n";
        } else {
            echo "   ✅ mob_type уже установлен правильно\n";
        }
    } else {
        echo "   ❌ Тестовый моб не найден\n";
    }

    // 5. Повторная проверка после исправления
    echo "\n5️⃣ Повторная проверка после исправления...\n";
    
    $filteredMobsAfter = Mob::where('location_id', $mineLocation->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->where(function ($query) {
            $query->whereNull('death_time')
                ->orWhere('death_time', '<', now()->subMinutes(5));
        })
        ->get();
    
    echo "   Мобов после исправления: {$filteredMobsAfter->count()}\n";
    
    foreach ($filteredMobsAfter as $mob) {
        echo "     - {$mob->name} (ID: {$mob->id}), mob_type: {$mob->mob_type}\n";
    }

    // 6. Тест автоатаки после исправления
    if ($filteredMobsAfter->count() > 0) {
        echo "\n6️⃣ Тест автоатаки после исправления...\n";
        
        // Очищаем старые метки
        MineMark::where('player_id', $user->id)->delete();
        
        // Создаем новую метку
        $mark = MineMark::create([
            'player_id' => $user->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'expires_at' => now()->addMinutes(10),
            'is_active' => true,
            'last_attack_at' => null,
            'attack_count' => 0,
        ]);
        
        // Устанавливаем правильную локацию игрока
        $user->statistics->current_location = $mineLocation->name;
        $user->statistics->save();
        
        echo "   Метка создана (ID: {$mark->id})\n";
        echo "   Локация игрока: {$mineLocation->name}\n";
        
        // Проверяем HP до атаки
        $playerHealthService = app(\App\Services\PlayerHealthService::class);
        $hpBefore = $playerHealthService->getCurrentHP($user);
        echo "   HP до атаки: {$hpBefore}\n";
        
        // Запускаем автоатаку
        $job = new \App\Jobs\MineAutoAttackJob();
        $job->handle(
            app(\App\Services\MineDetectionService::class),
            app(\App\Services\MineTargetDistributionService::class),
            app(\App\Services\BattleLogService::class),
            app(\App\Services\PlayerHealthService::class),
            app(\App\Services\CombatFormulaService::class),
            app(\App\Services\LogFormattingService::class)
        );
        
        echo "   ✅ Автоатака выполнена\n";
        
        // Проверяем результаты
        $markAfter = MineMark::find($mark->id);
        $hpAfter = $playerHealthService->getCurrentHP($user);
        
        echo "   Результаты:\n";
        echo "     - Последняя атака: " . ($markAfter && $markAfter->last_attack_at ? $markAfter->last_attack_at : 'НЕТ') . "\n";
        echo "     - Количество атак: " . ($markAfter ? $markAfter->attack_count : 0) . "\n";
        echo "     - HP после атаки: {$hpAfter}\n";
        echo "     - Урон нанесен: " . ($hpBefore > $hpAfter ? 'ДА (' . ($hpBefore - $hpAfter) . ')' : 'НЕТ') . "\n";
        
        if ($markAfter && $markAfter->last_attack_at && $hpBefore > $hpAfter) {
            echo "\n   🎉 АВТОАТАКА РАБОТАЕТ!\n";
        } else {
            echo "\n   ❌ Автоатака все еще не работает\n";
        }
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
