<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use Illuminate\Support\Facades\Redis;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ПРЯМАЯ ПРОВЕРКА REDIS ЛОГОВ ===\n";

try {
    $user = User::find(7);
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

    // 1. Проверяем все ключи, связанные с логами этого игрока
    echo "\n1️⃣ Поиск всех ключей логов для игрока...\n";
    $allKeys = Redis::keys("*{$user->id}*logs*");
    echo "   Найдено ключей: " . count($allKeys) . "\n";
    
    foreach ($allKeys as $key) {
        $count = Redis::llen($key);
        echo "     - {$key}: {$count} записей\n";
        
        if ($count > 0) {
            $firstEntry = Redis::lindex($key, 0);
            $entryData = json_decode($firstEntry, true);
            if ($entryData && isset($entryData['message'])) {
                $message = strip_tags($entryData['message']);
                if (strlen($message) > 50) {
                    $message = substr($message, 0, 50) . '...';
                }
                echo "       Первая запись: {$message}\n";
            }
        }
    }

    // 2. Проверяем конкретный ключ battle_logs:7:logs
    echo "\n2️⃣ Проверка основного ключа battle_logs:7:logs...\n";
    $mainKey = "battle_logs:7:logs";
    $exists = Redis::exists($mainKey);
    echo "   Ключ существует: " . ($exists ? 'ДА' : 'НЕТ') . "\n";
    
    if ($exists) {
        $count = Redis::llen($mainKey);
        echo "   Количество записей: {$count}\n";
        
        // Показываем последние 5 записей
        echo "   Последние 5 записей:\n";
        for ($i = 0; $i < min(5, $count); $i++) {
            $entry = Redis::lindex($mainKey, $i);
            $entryData = json_decode($entry, true);
            if ($entryData) {
                $timestamp = $entryData['timestamp'] ?? 'unknown';
                $message = strip_tags($entryData['message'] ?? '');
                if (strlen($message) > 60) {
                    $message = substr($message, 0, 60) . '...';
                }
                echo "     [{$i}] {$timestamp}: {$message}\n";
            }
        }
    }

    // 3. Создаем тестовую метку и запускаем автоатаку
    echo "\n3️⃣ Создание тестовой метки и запуск автоатаки...\n";
    
    // Очищаем старые метки
    MineMark::where('player_id', $user->id)->delete();
    
    // Создаем метку
    $mineLocation = MineLocation::where('is_active', true)->first();
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null,
        'attack_count' => 0,
    ]);
    
    // Устанавливаем правильную локацию игрока
    $user->statistics->current_location = $mineLocation->name;
    $user->statistics->save();
    
    echo "   Метка создана (ID: {$mark->id})\n";
    echo "   Локация игрока: {$mineLocation->name}\n";
    
    // Проверяем количество записей ДО автоатаки
    $countBefore = Redis::llen($mainKey);
    echo "   Записей в логе ДО автоатаки: {$countBefore}\n";
    
    // Запускаем автоатаку
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "   ✅ Автоатака выполнена\n";
    
    // Проверяем количество записей ПОСЛЕ автоатаки
    $countAfter = Redis::llen($mainKey);
    echo "   Записей в логе ПОСЛЕ автоатаки: {$countAfter}\n";
    
    if ($countAfter > $countBefore) {
        echo "   🎯 НОВАЯ ЗАПИСЬ ДОБАВЛЕНА!\n";
        
        // Показываем новую запись
        $newEntry = Redis::lindex($mainKey, 0);
        $newEntryData = json_decode($newEntry, true);
        if ($newEntryData) {
            $message = strip_tags($newEntryData['message'] ?? '');
            echo "   Новая запись: {$message}\n";
        }
    } else {
        echo "   ❌ Новая запись НЕ добавлена\n";
    }

    // 4. Проверяем все ключи еще раз
    echo "\n4️⃣ Проверка всех ключей после автоатаки...\n";
    $allKeysAfter = Redis::keys("*{$user->id}*logs*");
    echo "   Найдено ключей: " . count($allKeysAfter) . "\n";
    
    foreach ($allKeysAfter as $key) {
        $count = Redis::llen($key);
        echo "     - {$key}: {$count} записей\n";
    }

    // 5. Проверяем метку после атаки
    echo "\n5️⃣ Проверка метки после атаки...\n";
    $markAfter = MineMark::find($mark->id);
    if ($markAfter) {
        echo "   Метка найдена\n";
        echo "   Последняя атака: " . ($markAfter->last_attack_at ? $markAfter->last_attack_at : 'НЕТ') . "\n";
        echo "   Количество атак: {$markAfter->attack_count}\n";
        
        if ($markAfter->last_attack_at) {
            echo "   ✅ АТАКА ПРОИЗОШЛА!\n";
        } else {
            echo "   ❌ Атака НЕ произошла\n";
        }
    } else {
        echo "   ❌ Метка не найдена (удалена)\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
