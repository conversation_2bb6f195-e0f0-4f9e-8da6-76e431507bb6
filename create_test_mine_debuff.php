<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== СОЗДАНИЕ ТЕСТОВОГО ДЕБАФА 'ЗАМЕЧЕН' ===\n";

try {
    // 1. Находим пользователя admin
    $user = User::where('name', 'admin')->first();
    if (!$user) {
        echo "❌ Пользователь admin не найден\n";
        exit(1);
    }
    echo "✅ Пользователь найден: {$user->name} (ID: {$user->id})\n";

    // 2. Находим активную локацию рудника
    $mineLocation = MineLocation::where('is_active', true)->first();
    if (!$mineLocation) {
        echo "❌ Активная локация рудника не найдена\n";
        exit(1);
    }
    echo "✅ Локация рудника найдена: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    echo "   Основная локация ID: {$mineLocation->location_id}\n";

    // 3. Создаем дебаф через сервис
    echo "\n🎯 Создание дебафа 'Замечен'...\n";
    $mineDetectionService = app(MineDetectionService::class);
    
    $mark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
    
    if ($mark) {
        echo "✅ Дебаф успешно создан!\n";
        echo "   MineMark ID: {$mark->id}\n";
        echo "   Игрок: {$mark->player_id} ({$user->name})\n";
        echo "   Рудник: {$mark->mine_location_id} ({$mineLocation->name})\n";
        echo "   Локация: {$mark->location_id}\n";
        echo "   Истекает: {$mark->expires_at}\n";
        echo "   Активен: " . ($mark->is_active ? 'ДА' : 'НЕТ') . "\n";
        echo "   Время до истечения: " . $mark->expires_at->diffInSeconds(now()) . " секунд\n";
    } else {
        echo "❌ Не удалось создать дебаф\n";
        exit(1);
    }

    // 4. Проверяем создание ActiveEffect
    echo "\n🔍 Проверка ActiveEffect...\n";
    $activeEffect = \App\Models\ActiveEffect::where('target_type', 'player')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->where('ends_at', '>', now())
        ->first();
    
    if ($activeEffect) {
        echo "✅ ActiveEffect создан!\n";
        echo "   Effect ID: {$activeEffect->id}\n";
        echo "   Название: {$activeEffect->effect_name}\n";
        echo "   Истекает: {$activeEffect->ends_at}\n";
        echo "   Время до истечения: " . $activeEffect->ends_at->diffInSeconds(now()) . " секунд\n";
    } else {
        echo "❌ ActiveEffect не найден\n";
    }

    // 5. Запускаем тестовую автоатаку
    echo "\n🚀 Запуск тестовой автоатаки...\n";
    
    // Проверяем, есть ли мобы в рудниках
    $mineMobs = \App\Models\Mob::whereNotNull('mine_location_id')->where('hp', '>', 0)->count();
    echo "   Доступных мобов в рудниках: {$mineMobs}\n";
    
    if ($mineMobs > 0) {
        echo "   Запуск MineAutoAttackJob...\n";
        
        // Запускаем джоб синхронно для тестирования
        $job = new \App\Jobs\MineAutoAttackJob();
        $job->handle(
            app(\App\Services\MineDetectionService::class),
            app(\App\Services\MineTargetDistributionService::class),
            app(\App\Services\BattleLogService::class),
            app(\App\Services\PlayerHealthService::class),
            app(\App\Services\CombatFormulaService::class),
            app(\App\Services\LogFormattingService::class)
        );
        
        echo "   ✅ MineAutoAttackJob выполнен\n";
        
        // Проверяем обновление времени последней атаки
        $markAfterAttack = \App\Models\MineMark::find($mark->id);
        if ($markAfterAttack && $markAfterAttack->last_attack_at) {
            echo "   🎯 Время последней атаки обновлено: {$markAfterAttack->last_attack_at}\n";
            echo "   🔢 Количество атак: {$markAfterAttack->attack_count}\n";
        } else {
            echo "   ⚠️ Время последней атаки НЕ обновлено\n";
        }
    } else {
        echo "   ❌ Нет мобов для атаки\n";
    }

    echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
