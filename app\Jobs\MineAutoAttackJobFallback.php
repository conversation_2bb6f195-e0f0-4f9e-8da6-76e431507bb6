<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Models\ActiveEffect;
use App\Services\MineDetectionServiceFallback;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;
use App\Events\MobAttackedPlayer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Fallback Job для автоматической атаки мобов на замеченных игроков в рудниках
 * Использует ActiveEffect вместо MineMark пока таблица mine_marks не создана
 */
class MineAutoAttackJobFallback implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 120;

    /**
     * Выполнить задачу автоатаки мобов в рудниках (fallback версия)
     */
    public function handle(
        MineDetectionServiceFallback $mineDetectionService,
        BattleLogService $battleLogService,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        LogFormattingService $logFormatter
    ): void {
        $lockKey = 'mine_auto_attack_fallback_lock';
        $lockTimeout = 60;

        try {
            $lock = Redis::set($lockKey, time(), 'EX', $lockTimeout, 'NX');
            if (!$lock) {
                Log::info('MineAutoAttackJobFallback: Другая задача автоатаки уже выполняется');
                return;
            }

            Log::info('MineAutoAttackJobFallback: Начинаем обработку автоатак мобов в рудниках (fallback)');

            // Очистка истекших меток перед обработкой
            $mineDetectionService->cleanupExpiredMarks();

            // Получаем всех замеченных игроков в рудниках
            $markedPlayers = $this->getActiveMarkedPlayersFallback($mineDetectionService);

            if (empty($markedPlayers)) {
                Log::info('MineAutoAttackJobFallback: Нет замеченных игроков в рудниках');
                return;
            }

            $attacksProcessed = 0;
            $maxAttacksPerRun = 20;

            foreach ($markedPlayers as $playerData) {
                if ($attacksProcessed >= $maxAttacksPerRun) {
                    Log::info('MineAutoAttackJobFallback: Достигнуто максимальное количество атак за раз');
                    break;
                }

                try {
                    $result = $this->processPlayerAttackFallback(
                        $playerData,
                        $mineDetectionService,
                        $battleLogService,
                        $playerHealthService,
                        $combatFormulaService,
                        $logFormatter
                    );

                    if ($result) {
                        $attacksProcessed++;
                        usleep(100000); // 0.1 секунды
                    }
                } catch (\Exception $e) {
                    Log::error('MineAutoAttackJobFallback: Ошибка при обработке атаки на игрока', [
                        'player_id' => $playerData['player_id'] ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('MineAutoAttackJobFallback: Завершена обработка автоатак', [
                'attacks_processed' => $attacksProcessed,
                'marked_players_found' => count($markedPlayers)
            ]);

        } catch (\Exception $e) {
            Log::error('MineAutoAttackJobFallback: Критическая ошибка', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } finally {
            Redis::del($lockKey);
        }
    }

    /**
     * Получить всех замеченных игроков в рудниках (fallback версия)
     */
    private function getActiveMarkedPlayersFallback(MineDetectionServiceFallback $mineDetectionService): array
    {
        try {
            // Получаем все активные эффекты обнаружения
            $effects = ActiveEffect::with(['target.profile'])
                ->where('effect_type', 'mine_detection_debuff')
                ->where('target_type', 'App\\Models\\User')
                ->where('ends_at', '>', now())
                ->get();

            $validPlayers = [];

            foreach ($effects as $effect) {
                if (
                    !$effect->target ||
                    !$effect->target->profile ||
                    $effect->target->profile->current_health <= 0
                ) {
                    continue;
                }

                $mineLocationId = $effect->effect_data['mine_location_id'] ?? null;
                $locationId = $effect->effect_data['location_id'] ?? null;

                if (!$mineLocationId || !$locationId) {
                    continue;
                }

                // Проверяем, что игрок находится в нужной локации
                if ($effect->target->current_location_id !== $locationId) {
                    // Игрок покинул локацию, удаляем дебаф
                    $mineDetectionService->removeDetectionDebuff(
                        $effect->target_id,
                        $locationId,
                        $mineLocationId
                    );
                    continue;
                }

                // Найдем локацию рудника
                $mineLocation = MineLocation::find($mineLocationId);
                if (!$mineLocation) {
                    continue;
                }

                $validPlayers[] = [
                    'effect_id' => $effect->id,
                    'player_id' => $effect->target_id,
                    'player' => $effect->target,
                    'mine_location_id' => $mineLocationId,
                    'mine_location' => $mineLocation,
                    'location_id' => $locationId,
                    'location_name' => $effect->effect_data['location_name'] ?? $mineLocation->name,
                    'expires_at' => $effect->ends_at,
                    'detected_at' => $effect->effect_data['detected_at'] ?? null
                ];
            }

            return $validPlayers;

        } catch (\Exception $e) {
            Log::error('MineAutoAttackJobFallback: Ошибка при получении замеченных игроков', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Обработать атаку на конкретного игрока (fallback версия)
     */
    private function processPlayerAttackFallback(
        array $playerData,
        MineDetectionServiceFallback $mineDetectionService,
        BattleLogService $battleLogService,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        LogFormattingService $logFormatter
    ): bool {
        $player = $playerData['player'];
        $mineLocation = $playerData['mine_location'];

        // Проверяем кулдаун атак на основе последней атаки
        $effect = ActiveEffect::find($playerData['effect_id']);
        if ($effect && isset($effect->effect_data['last_attack_at'])) {
            $lastAttackAt = Carbon::parse($effect->effect_data['last_attack_at']);
            if ($lastAttackAt->addSeconds(30)->isFuture()) {
                return false; // Слишком рано для повторной атаки
            }
        }

        // Находим доступных мобов в руднике
        $availableMobs = $this->getAvailableMobsInMineFallback($mineLocation);

        if ($availableMobs->isEmpty()) {
            Log::info('MineAutoAttackJobFallback: Нет доступных мобов в руднике', [
                'mine_location_id' => $mineLocation->id,
                'location_name' => $mineLocation->name
            ]);
            return false;
        }

        // Выбираем случайного моба
        $mob = $availableMobs->random();

        // Рассчитываем урон
        $baseDamage = rand(5, 15);
        $damage = $combatFormulaService->calculateMobDamage($mob, $player, $baseDamage);

        // Применяем урон к игроку
        $damageResult = $playerHealthService->applyDamage(
            $player,
            $damage,
            "mine_mob_detection_fallback:{$mob->id}"
        );

        // Создаем запись в боевом логе
        $logMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $player->name,
            $damage,
            $mineLocation->name
        );

        $battleLogKey = "battle_logs:{$player->id}";
        $battleLogService->addLog($battleLogKey, $logMessage);

        // Запускаем событие атаки моба
        event(new MobAttackedPlayer($mob, $player, $damage, 'mine_detection_fallback'));

        // Обновляем время в эффекте
        $effect = ActiveEffect::find($playerData['effect_id']);
        if ($effect) {
            $effectData = $effect->effect_data;
            $effectData['last_attack_at'] = now()->toISOString();
            $effect->effect_data = $effectData;
            $effect->save();
        }

        Log::info('MineAutoAttackJobFallback: Успешная атака моба на замеченного игрока', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $player->id,
            'player_name' => $player->name,
            'damage' => $damage,
            'mine_location' => $mineLocation->name,
            'player_health_after' => $damageResult['current_health'] ?? 'unknown'
        ]);

        return true;
    }

    /**
     * Получить доступных мобов в руднике (fallback версия)
     */
    private function getAvailableMobsInMineFallback(MineLocation $mineLocation)
    {
        return Mob::where('location_id', $mineLocation->location_id)
            ->where('mine_location_id', $mineLocation->id)
            ->where('current_health', '>', 0)
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhere('death_time', '<', now()->subMinutes(5));
            })
            ->get();
    }
}