<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ПРОВЕРКА ПРАВИЛЬНОГО КЛЮЧА REDIS ===\n";
    
    $admin = User::where('name', 'admin')->first();
    $battleLogService = app(BattleLogService::class);
    
    echo "Игрок: {$admin->name} (ID: {$admin->id})\n";
    
    // 1. Проверяем ключи до добавления
    echo "\n1️⃣ Ключи до добавления:\n";
    
    $baseKey = "battle_logs:{$admin->id}";
    $redisKey = "{$baseKey}:logs";
    
    echo "   Базовый ключ: {$baseKey}\n";
    echo "   Redis ключ: {$redisKey}\n";
    
    $logsBase = Redis::lrange($baseKey, 0, -1);
    $logsRedis = Redis::lrange($redisKey, 0, -1);
    
    echo "   Логов в базовом ключе: " . count($logsBase) . "\n";
    echo "   Логов в Redis ключе: " . count($logsRedis) . "\n";
    
    // 2. Добавляем лог через BattleLogService
    echo "\n2️⃣ Добавление лога через BattleLogService:\n";
    
    $testMessage = "Тестовое сообщение " . time();
    $battleLogService->addLog($baseKey, $testMessage, 'test');
    
    echo "   ✅ Лог добавлен\n";
    
    // 3. Проверяем ключи после добавления
    echo "\n3️⃣ Ключи после добавления:\n";
    
    $logsBaseAfter = Redis::lrange($baseKey, 0, -1);
    $logsRedisAfter = Redis::lrange($redisKey, 0, -1);
    
    echo "   Логов в базовом ключе: " . count($logsBaseAfter) . "\n";
    echo "   Логов в Redis ключе: " . count($logsRedisAfter) . "\n";
    
    // 4. Показываем содержимое правильного ключа
    if (count($logsRedisAfter) > count($logsRedis)) {
        echo "\n4️⃣ ✅ Лог добавлен в правильный ключ: {$redisKey}\n";
        
        $newLog = json_decode($logsRedisAfter[0], true);
        echo "   Новый лог:\n";
        echo "     Сообщение: " . strip_tags($newLog['message'] ?? 'неизвестно') . "\n";
        echo "     Тип: " . ($newLog['type'] ?? 'неизвестно') . "\n";
        echo "     Время: " . ($newLog['timestamp'] ?? 'неизвестно') . "\n";
    } else {
        echo "\n4️⃣ ❌ Лог НЕ добавлен\n";
    }
    
    // 5. Проверяем метод getLogs
    echo "\n5️⃣ Проверка getLogs:\n";
    
    $retrievedLogs = $battleLogService->getLogs($baseKey, null, 5);
    echo "   Логов через getLogs(): " . count($retrievedLogs) . "\n";
    
    if (!empty($retrievedLogs)) {
        echo "   Последние логи:\n";
        foreach (array_slice($retrievedLogs, 0, 3) as $index => $log) {
            echo "     " . ($index + 1) . ". " . strip_tags($log['message'] ?? 'неизвестно') . "\n";
        }
    }
    
    echo "\n=== ПРОВЕРКА ЗАВЕРШЕНА ===\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
