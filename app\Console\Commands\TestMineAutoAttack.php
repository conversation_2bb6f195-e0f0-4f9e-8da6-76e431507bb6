<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Jobs\MineAutoAttackJob;
use App\Services\MineDetectionService;
use App\Services\BattleLogService;

class TestMineAutoAttack extends Command
{
    protected $signature = 'test:mine-auto-attack';
    protected $description = 'Тестирование полного цикла автоатак мобов в рудниках';

    public function handle()
    {
        $this->info('=== Тестирование автоатак мобов в рудниках ===');

        // 1. Находим тестового пользователя и локацию с мобом
        $testUser = User::first();

        // Ищем локацию рудника, где есть мобы
        $mineLocationWithMob = null;
        $mineLocations = MineLocation::all();

        foreach ($mineLocations as $location) {
            $mobCount = Mob::where('location_id', $location->location_id)
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->count();

            if ($mobCount > 0) {
                $mineLocationWithMob = $location;
                break;
            }
        }

        if (!$mineLocationWithMob) {
            // Ищем любую локацию рудника
            $mineLocationWithMob = MineLocation::first();
        }

        if (!$testUser || !$mineLocationWithMob) {
            $this->error('❌ Не найден тестовый пользователь или локация рудника');
            return;
        }

        $this->info("Тестовый пользователь: {$testUser->name} (ID: {$testUser->id})");
        $this->info("Локация рудника: {$mineLocationWithMob->name} (ID: {$mineLocationWithMob->id})");

        // 2. Проверяем мобов в локации
        $mobsInLocation = Mob::where('location_id', $mineLocationWithMob->location_id)
            ->where('mob_type', 'mine')
            ->where('hp', '>', 0)
            ->get();

        $this->info("Мобов в локации: " . $mobsInLocation->count());
        foreach ($mobsInLocation as $mob) {
            $this->line("- {$mob->name} (ID: {$mob->id}, HP: {$mob->hp})");
        }

        if ($mobsInLocation->isEmpty()) {
            $this->error('❌ Нет доступных мобов для атаки');
            return;
        }

        // 3. Создаем метку обнаружения
        $mineDetectionService = app(MineDetectionService::class);
        $mark = $mineDetectionService->applyDetectionDebuff($testUser, $mineLocationWithMob);

        if (!$mark) {
            $this->error('❌ Не удалось создать метку обнаружения');
            return;
        }

        $this->info("✅ Метка обнаружения создана (ID: {$mark->id})");

        // 4. Сохраняем начальное HP игрока
        $initialHp = $testUser->profile->current_hp ?? 100;
        $this->info("Начальное HP игрока: {$initialHp}");

        // 5. Запускаем MineAutoAttackJob
        $this->info("\n=== Запуск автоатаки ===");

        try {
            $job = new MineAutoAttackJob();
            $job->handle(
                app(\App\Services\MineDetectionService::class),
                app(\App\Services\MineTargetDistributionService::class),
                app(\App\Services\BattleLogService::class),
                app(\App\Services\PlayerHealthService::class),
                app(\App\Services\CombatFormulaService::class),
                app(\App\Services\LogFormattingService::class)
            );
            $this->info("✅ MineAutoAttackJob выполнен");
        } catch (\Exception $e) {
            $this->error("❌ Ошибка в MineAutoAttackJob: " . $e->getMessage());
            $this->error("Trace: " . $e->getTraceAsString());
        }

        // 6. Проверяем результаты
        $testUser->refresh();
        $finalHp = $testUser->profile->current_hp ?? 100;
        $this->info("Финальное HP игрока: {$finalHp}");

        if ($finalHp < $initialHp) {
            $damage = $initialHp - $finalHp;
            $this->info("✅ Игрок получил урон: {$damage} HP");
        } else {
            $this->warn("⚠️ Игрок не получил урон");
        }

        // 7. Проверяем обновление метки
        try {
            $mark->refresh();
            if ($mark->last_attack_at) {
                $this->info("✅ Время последней атаки обновлено: {$mark->last_attack_at}");
                $this->info("Количество атак: {$mark->attack_count}");
            } else {
                $this->warn("⚠️ Время последней атаки не обновлено");
            }
        } catch (\Exception $e) {
            $this->warn("⚠️ Метка была удалена во время выполнения джоба");
        }

        // 8. Проверяем журнал боя
        $this->info("\n=== Проверка журнала боя ===");
        $this->info("Журнал боя должен содержать записи об атаке моба");

        // 9. Очистка - удаляем тестовую метку (если она еще существует)
        try {
            $mark->refresh();
            $mark->delete();
            $this->info("\n🧹 Тестовая метка удалена");
        } catch (\Exception $e) {
            $this->info("\n🧹 Тестовая метка уже была удалена");
        }

        $this->info("\n=== Тестирование завершено ===");
    }
}
