<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\BattleLogService;
use App\Services\LogFormattingService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДИАГНОСТИКА ПРОБЛЕМЫ С ЖУРНАЛОМ БОЯ ===\n";

try {
    $user = User::find(7);
    $battleLogService = app(BattleLogService::class);
    $logFormatter = app(LogFormattingService::class);
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

    // 1. Тестируем форматирование сообщения
    echo "\n1️⃣ Тест форматирования сообщения...\n";
    $testMessage = $logFormatter->formatMineDetectionAttack(
        'Тестовый моб',
        'admin',
        15,
        'Тестовый рудник'
    );
    
    echo "   Сформированное сообщение:\n";
    echo "   HTML: {$testMessage}\n";
    echo "   Текст: " . strip_tags($testMessage) . "\n";

    // 2. Тестируем добавление лога
    echo "\n2️⃣ Тест добавления лога...\n";
    $battleLogKey = "battle_logs:{$user->id}";
    echo "   Ключ журнала: {$battleLogKey}\n";
    
    // Проверяем количество логов до добавления
    $logsBefore = $battleLogService->getLogs($battleLogKey, null, 10);
    echo "   Логов до добавления: " . count($logsBefore) . "\n";
    
    // Добавляем тестовый лог
    $battleLogService->addLog($battleLogKey, $testMessage, 'danger');
    echo "   ✅ Лог добавлен\n";
    
    // Проверяем количество логов после добавления
    $logsAfter = $battleLogService->getLogs($battleLogKey, null, 10);
    echo "   Логов после добавления: " . count($logsAfter) . "\n";
    
    if (count($logsAfter) > count($logsBefore)) {
        echo "   ✅ Лог успешно добавлен в журнал!\n";
        
        // Показываем последний лог
        if (!empty($logsAfter)) {
            $lastLog = $logsAfter[0];
            if (is_array($lastLog) && isset($lastLog['message'])) {
                echo "   Последний лог: " . strip_tags($lastLog['message']) . "\n";
            }
        }
    } else {
        echo "   ❌ Лог НЕ добавлен в журнал\n";
    }

    // 3. Проверяем Redis напрямую
    echo "\n3️⃣ Проверка Redis напрямую...\n";
    $redisKey = "{$battleLogKey}:logs";
    echo "   Redis ключ: {$redisKey}\n";
    
    $redisExists = \Illuminate\Support\Facades\Redis::exists($redisKey);
    echo "   Ключ существует в Redis: " . ($redisExists ? 'ДА' : 'НЕТ') . "\n";
    
    if ($redisExists) {
        $redisCount = \Illuminate\Support\Facades\Redis::llen($redisKey);
        echo "   Количество записей в Redis: {$redisCount}\n";
        
        if ($redisCount > 0) {
            $firstEntry = \Illuminate\Support\Facades\Redis::lindex($redisKey, 0);
            $entryData = json_decode($firstEntry, true);
            if ($entryData && isset($entryData['message'])) {
                echo "   Первая запись: " . strip_tags($entryData['message']) . "\n";
            }
        }
    }

    // 4. Тестируем создание метки и автоатаку с логированием
    echo "\n4️⃣ Тест полного цикла с автоатакой...\n";
    
    // Очищаем старые метки
    MineMark::where('player_id', $user->id)->delete();
    
    // Создаем метку
    $mineLocation = MineLocation::where('is_active', true)->first();
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null,
        'attack_count' => 0,
    ]);
    
    // Устанавливаем правильную локацию игрока
    $user->statistics->current_location = $mineLocation->name;
    $user->statistics->save();
    
    echo "   Метка создана (ID: {$mark->id})\n";
    echo "   Локация игрока установлена: {$mineLocation->name}\n";
    
    // Проверяем количество логов до автоатаки
    $logsBeforeAttack = $battleLogService->getLogs($battleLogKey, null, 10);
    echo "   Логов до автоатаки: " . count($logsBeforeAttack) . "\n";
    
    // Запускаем автоатаку
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "   ✅ Автоатака выполнена\n";
    
    // Проверяем количество логов после автоатаки
    $logsAfterAttack = $battleLogService->getLogs($battleLogKey, null, 10);
    echo "   Логов после автоатаки: " . count($logsAfterAttack) . "\n";
    
    if (count($logsAfterAttack) > count($logsBeforeAttack)) {
        echo "   ✅ Автоатака добавила лог в журнал!\n";
        
        // Показываем новые логи
        $newLogsCount = count($logsAfterAttack) - count($logsBeforeAttack);
        echo "   Новых логов: {$newLogsCount}\n";
        
        for ($i = 0; $i < min($newLogsCount, 3); $i++) {
            $log = $logsAfterAttack[$i];
            if (is_array($log) && isset($log['message'])) {
                echo "     - " . strip_tags($log['message']) . "\n";
            }
        }
    } else {
        echo "   ❌ Автоатака НЕ добавила лог в журнал\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
