<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Models\User;
use App\Models\ActiveEffect;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДИАГНОСТИКА СИСТЕМЫ АВТОАТАК В РУДНИКАХ ===\n";

try {
    // 1. Проверяем таблицу mine_marks
    echo "\n1️⃣ Проверка таблицы mine_marks...\n";
    $hasMineMarks = Schema::hasTable('mine_marks');
    echo "   Таблица существует: " . ($hasMineMarks ? 'ДА' : 'НЕТ') . "\n";

    if ($hasMineMarks) {
        $columns = Schema::getColumnListing('mine_marks');
        echo "   Колонки: " . implode(', ', $columns) . "\n";

        $marksCount = MineMark::count();
        $activeMarksCount = MineMark::where('is_active', true)->where('expires_at', '>', now())->count();
        echo "   Всего меток: {$marksCount}\n";
        echo "   Активных меток: {$activeMarksCount}\n";

        if ($activeMarksCount > 0) {
            echo "   Активные метки:\n";
            $activeMarks = MineMark::where('is_active', true)
                ->where('expires_at', '>', now())
                ->with(['player', 'mineLocation'])
                ->get();

            foreach ($activeMarks as $mark) {
                $playerName = $mark->player->name ?? 'Unknown';
                $mineName = $mark->mineLocation->name ?? 'Unknown';
                $timeRemaining = $mark->expires_at->diffInSeconds(now());
                echo "     - Игрок: {$playerName}, Рудник: {$mineName}, Осталось: {$timeRemaining}с\n";
            }
        }
    }

    // 2. Проверяем локации рудников
    echo "\n2️⃣ Проверка локаций рудников...\n";
    $mineLocations = MineLocation::where('is_active', true)->count();
    echo "   Активных локаций рудников: {$mineLocations}\n";

    if ($mineLocations > 0) {
        $firstMineLocation = MineLocation::where('is_active', true)->first();
        echo "   Первая локация: {$firstMineLocation->name} (ID: {$firstMineLocation->id})\n";
        echo "   Основная локация ID: {$firstMineLocation->location_id}\n";
    }

    // 3. Проверяем мобов в рудниках
    echo "\n3️⃣ Проверка мобов в рудниках...\n";
    $mineMobs = Mob::whereNotNull('mine_location_id')->where('hp', '>', 0)->count();
    echo "   Живых мобов в рудниках: {$mineMobs}\n";

    if ($mineMobs > 0) {
        $firstMob = Mob::whereNotNull('mine_location_id')->where('hp', '>', 0)->first();
        echo "   Первый моб: {$firstMob->name} (ID: {$firstMob->id})\n";
        echo "   Локация: {$firstMob->location_id}, Рудник: {$firstMob->mine_location_id}\n";
        echo "   HP: {$firstMob->hp}/{$firstMob->max_hp}\n";

        // Показываем несколько мобов для полной картины
        $allMineMobs = Mob::whereNotNull('mine_location_id')->where('hp', '>', 0)->limit(5)->get();
        echo "   Список мобов в рудниках:\n";
        foreach ($allMineMobs as $mob) {
            echo "     - {$mob->name} (ID: {$mob->id}) HP: {$mob->hp}/{$mob->max_hp} Локация: {$mob->location_id} Рудник: {$mob->mine_location_id}\n";
        }
    }

    // 4. Проверяем пользователя admin
    echo "\n4️⃣ Проверка пользователя admin...\n";
    $admin = User::where('name', 'admin')->first();
    if ($admin) {
        echo "   Пользователь найден: {$admin->name} (ID: {$admin->id})\n";
        echo "   HP: {$admin->hp}/{$admin->max_hp}\n";
        echo "   Локация: {$admin->location_id}\n";
    } else {
        echo "   ❌ Пользователь admin не найден\n";
    }

    // 5. Проверяем активные эффекты "Замечен"
    echo "\n5️⃣ Проверка активных эффектов 'Замечен'...\n";
    $detectionEffects = ActiveEffect::where('effect_type', 'mine_detection_debuff')
        ->where('target_type', 'player')
        ->where('ends_at', '>', now())
        ->count();
    echo "   Активных эффектов 'Замечен': {$detectionEffects}\n";

    if ($detectionEffects > 0) {
        $effects = ActiveEffect::where('effect_type', 'mine_detection_debuff')
            ->where('target_type', 'player')
            ->where('ends_at', '>', now())
            ->get();

        foreach ($effects as $effect) {
            $user = User::find($effect->target_id);
            $userName = $user ? $user->name : 'Unknown';
            $timeRemaining = $effect->ends_at->diffInSeconds(now());
            echo "     - Игрок: {$userName}, Осталось: {$timeRemaining}с\n";
        }
    }

    // 6. Проверяем планировщик задач
    echo "\n6️⃣ Проверка планировщика задач...\n";
    echo "   Для работы автоатак должен быть запущен планировщик:\n";
    echo "   php artisan schedule:work\n";
    echo "   Или настроен cron для: php artisan schedule:run\n";

    // 7. Проверяем Redis
    echo "\n7️⃣ Проверка Redis...\n";
    try {
        \Illuminate\Support\Facades\Redis::ping();
        echo "   Redis доступен: ДА\n";
    } catch (\Exception $e) {
        echo "   Redis доступен: НЕТ - " . $e->getMessage() . "\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка диагностики: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
