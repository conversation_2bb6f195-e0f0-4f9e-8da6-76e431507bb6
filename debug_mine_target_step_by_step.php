<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Models\Mob;
use App\Services\MineTargetDistributionService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ПОШАГОВАЯ ДИАГНОСТИКА MineTargetDistributionService ===\n";

    // 1. Подготовка данных
    echo "\n1️⃣ Подготовка данных...\n";
    
    $admin = User::where('name', 'admin')->first();
    if (!$admin) {
        echo "❌ Пользователь admin не найден\n";
        exit(1);
    }
    echo "   ✅ Пользователь: {$admin->name} (ID: {$admin->id})\n";
    
    $mineLocation = MineLocation::where('name', 'Тарнмор')->first();
    if (!$mineLocation) {
        echo "❌ Локация рудника Тарнмор не найдена\n";
        exit(1);
    }
    echo "   ✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";

    // 2. Создаем метку если её нет
    echo "\n2️⃣ Создание/проверка метки...\n";
    
    $mark = MineMark::where('player_id', $admin->id)
        ->where('mine_location_id', $mineLocation->id)
        ->where('is_active', true)
        ->where('expires_at', '>', now())
        ->first();
    
    if (!$mark) {
        $mark = MineMark::create([
            'player_id' => $admin->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'is_active' => true,
            'expires_at' => now()->addMinutes(30),
            'last_attack_at' => null,
            'attack_count' => 0
        ]);
        echo "   ✅ Метка создана (ID: {$mark->id})\n";
    } else {
        echo "   ✅ Метка найдена (ID: {$mark->id})\n";
    }
    
    // Обновляем локацию игрока
    $admin->statistics->update(['current_location' => $mineLocation->name]);
    echo "   ✅ Локация игрока обновлена: {$mineLocation->name}\n";

    // 3. Формируем данные игроков как в реальном коде
    echo "\n3️⃣ Формирование данных игроков...\n";
    
    $markedPlayers = collect([
        [
            'mark_id' => $mark->id,
            'player_id' => $mark->player_id,
            'player' => $mark->player,
            'mine_location_id' => $mark->mine_location_id,
            'mine_location' => $mark->mineLocation,
            'location_id' => $mark->location_id,
            'location_name' => $mark->location_name,
            'expires_at' => $mark->expires_at,
            'last_attack_at' => $mark->last_attack_at,
            'attack_count' => $mark->attack_count ?? 0
        ]
    ]);
    
    echo "   ✅ Данные игроков сформированы: {$markedPlayers->count()} игроков\n";
    echo "   Структура данных:\n";
    foreach ($markedPlayers as $playerData) {
        echo "     - Игрок: {$playerData['player']->name}\n";
        echo "     - Ключ 'player' существует: " . (isset($playerData['player']) ? 'ДА' : 'НЕТ') . "\n";
        echo "     - Тип объекта player: " . get_class($playerData['player']) . "\n";
    }

    // 4. Создаем сервис и тестируем каждый шаг
    echo "\n4️⃣ Пошаговое тестирование MineTargetDistributionService...\n";
    
    $targetDistributionService = app(MineTargetDistributionService::class);
    
    // Шаг 1: Проверяем isEmpty
    echo "\n   Шаг 1: Проверка isEmpty...\n";
    $isEmpty = $markedPlayers->isEmpty();
    echo "     markedPlayers->isEmpty(): " . ($isEmpty ? 'true' : 'false') . "\n";
    if ($isEmpty) {
        echo "     ❌ ВЫХОД: Коллекция пуста\n";
        exit(1);
    }
    
    // Шаг 2: Получаем доступных мобов
    echo "\n   Шаг 2: Получение доступных мобов...\n";
    $availableMobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mob_type', 'mine')
        ->where('hp', '>', 0)
        ->where(function ($query) {
            $query->whereNull('death_time')
                ->orWhere('death_time', '<', now()->subMinutes(5));
        })
        ->get();
    
    echo "     Найдено мобов: {$availableMobs->count()}\n";
    foreach ($availableMobs as $mob) {
        echo "       - {$mob->name} (ID: {$mob->id}, HP: {$mob->hp}, Сила: {$mob->strength})\n";
    }
    
    if ($availableMobs->isEmpty()) {
        echo "     ❌ ВЫХОД: Нет доступных мобов\n";
        exit(1);
    }
    
    // Шаг 3: Получаем текущие назначения
    echo "\n   Шаг 3: Получение текущих назначений из Redis...\n";
    $redisKey = 'mine_mob_targets:' . $mineLocation->id;
    $assignments = Redis::hgetall($redisKey);
    echo "     Redis ключ: {$redisKey}\n";
    echo "     Назначений в Redis: " . count($assignments) . "\n";
    
    $currentAssignments = [];
    foreach ($assignments as $playerId => $mobIds) {
        $mobs = json_decode($mobIds, true) ?: [];
        $currentAssignments[$playerId] = $mobs;
        echo "       - Игрок {$playerId}: " . count($mobs) . " мобов\n";
    }
    
    // Шаг 4: Фильтруем игроков по кулдауну
    echo "\n   Шаг 4: Фильтрация игроков по кулдауну...\n";
    
    $availablePlayers = $markedPlayers->filter(function ($playerData) {
        echo "       Проверяем игрока: {$playerData['player']->name}\n";
        
        $lastAttackAt = $playerData['last_attack_at'] ?? null;
        echo "         last_attack_at: " . ($lastAttackAt ? $lastAttackAt : 'null') . "\n";
        
        if (!$lastAttackAt) {
            echo "         ✅ Доступен (нет предыдущих атак)\n";
            return true;
        }
        
        $cooldownEnd = \Carbon\Carbon::parse($lastAttackAt)->addSeconds(25);
        $isPast = $cooldownEnd->isPast();
        echo "         Кулдаун до: {$cooldownEnd}\n";
        echo "         Кулдаун прошел: " . ($isPast ? 'ДА' : 'НЕТ') . "\n";
        
        return $isPast;
    });
    
    echo "     Игроков после фильтра: {$availablePlayers->count()}\n";
    
    if ($availablePlayers->isEmpty()) {
        echo "     ❌ ВЫХОД: Все игроки в кулдауне\n";
        exit(1);
    }
    
    // Шаг 5: Выбираем оптимальную цель
    echo "\n   Шаг 5: Выбор оптимальной цели...\n";
    
    // Проверяем игроков с низким HP
    $lowHpPlayers = $availablePlayers->filter(function ($playerData) {
        $player = $playerData['player'];
        echo "       Проверяем HP игрока {$player->name}:\n";
        echo "         current_hp: {$player->profile->current_hp}\n";
        echo "         max_hp: {$player->profile->max_hp}\n";
        
        $hpPercent = ($player->profile->current_hp / $player->profile->max_hp) * 100;
        echo "         HP процент: {$hpPercent}%\n";
        echo "         Низкий HP (<= 30%): " . ($hpPercent <= 30 ? 'ДА' : 'НЕТ') . "\n";
        
        return $hpPercent <= 30;
    });
    
    echo "     Игроков с низким HP: {$lowHpPlayers->count()}\n";
    
    // Выбираем из какой группы работать
    $targetPlayers = $lowHpPlayers->isNotEmpty() ? $lowHpPlayers : $availablePlayers;
    echo "     Работаем с группой: " . ($lowHpPlayers->isNotEmpty() ? 'низкий HP' : 'все доступные') . "\n";
    
    // Сортируем по количеству мобов и HP
    $playersByMobCount = $targetPlayers->map(function ($playerData) use ($currentAssignments) {
        $player = $playerData['player'];
        $mobCount = count($currentAssignments[$player->id] ?? []);
        $hpPercent = ($player->profile->current_hp / $player->profile->max_hp) * 100;
        
        echo "       Игрок {$player->name}: {$mobCount} мобов, HP: {$hpPercent}%\n";
        
        return [
            'player' => $player,
            'mob_count' => $mobCount,
            'hp_percent' => $hpPercent
        ];
    });
    
    $sorted = $playersByMobCount->sortBy([
        ['mob_count', 'asc'],
        ['hp_percent', 'asc']
    ]);
    
    echo "     Сортировка по приоритету:\n";
    foreach ($sorted as $data) {
        echo "       - {$data['player']->name}: {$data['mob_count']} мобов, HP: {$data['hp_percent']}%\n";
    }
    
    $selected = $sorted->first();
    if (!$selected) {
        echo "     ❌ ВЫХОД: Не удалось выбрать игрока\n";
        exit(1);
    }
    
    $targetPlayer = $selected['player'];
    echo "     ✅ Выбран игрок: {$targetPlayer->name} (ID: {$targetPlayer->id})\n";
    
    // Шаг 6: Выбираем моба для цели
    echo "\n   Шаг 6: Выбор моба для атаки...\n";
    
    $currentMobCount = count($currentAssignments[$targetPlayer->id] ?? []);
    echo "     Текущее количество мобов у игрока: {$currentMobCount}\n";
    echo "     Максимум мобов на игрока: 3\n";
    
    if ($currentMobCount >= 3) {
        $switchChance = rand(1, 100);
        echo "     Лимит превышен, шанс переключения: {$switchChance}% (нужно <= 20%)\n";
        if ($switchChance > 20) {
            echo "     ❌ ВЫХОД: Лимит превышен и нет переключения\n";
            exit(1);
        }
        echo "     ✅ Переключение разрешено\n";
    }
    
    // Выбираем моба по силе
    $playerHpPercent = ($targetPlayer->profile->current_hp / $targetPlayer->profile->max_hp) * 100;
    echo "     HP игрока: {$playerHpPercent}%\n";
    
    if ($playerHpPercent <= 30) {
        echo "     Выбираем слабого моба (HP <= 30%)\n";
        $weakMobs = $availableMobs->sortBy('strength')->take(ceil($availableMobs->count() * 0.6));
        echo "     Доступно слабых мобов: {$weakMobs->count()}\n";
        $selectedMob = $weakMobs->random();
    } elseif ($playerHpPercent >= 80) {
        echo "     Выбираем сильного моба (HP >= 80%)\n";
        $strongMobs = $availableMobs->sortByDesc('strength')->take(ceil($availableMobs->count() * 0.4));
        echo "     Доступно сильных мобов: {$strongMobs->count()}\n";
        $selectedMob = $strongMobs->random();
    } else {
        echo "     Выбираем случайного моба (HP 30-80%)\n";
        $selectedMob = $availableMobs->random();
    }
    
    if (!$selectedMob) {
        echo "     ❌ ВЫХОД: Не удалось выбрать моба\n";
        exit(1);
    }
    
    echo "     ✅ Выбран моб: {$selectedMob->name} (ID: {$selectedMob->id}, Сила: {$selectedMob->strength})\n";
    
    // Шаг 7: Тестируем реальный вызов метода
    echo "\n   Шаг 7: Тестирование реального вызова getOptimalMobPlayerPair...\n";
    
    $result = $targetDistributionService->getOptimalMobPlayerPair($markedPlayers, $mineLocation);
    
    if ($result) {
        echo "     ✅ УСПЕХ! Метод вернул пару:\n";
        echo "       Моб: {$result['mob']->name} (ID: {$result['mob']->id})\n";
        echo "       Игрок: {$result['player']->name} (ID: {$result['player']->id})\n";
    } else {
        echo "     ❌ ПРОВАЛ! Метод вернул null\n";
        echo "     🤔 Все шаги прошли успешно, но метод все равно вернул null\n";
        echo "     Это указывает на проблему в самом методе MineTargetDistributionService\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
