<?php

require_once __DIR__ . '/vendor/autoload.php';

use Carbon\Carbon;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДИАГНОСТИКА ПРОБЛЕМЫ С ВРЕМЕНЕМ ===\n";

try {
    // 1. Проверяем текущее время
    echo "1️⃣ Проверка времени...\n";
    $now = now();
    $carbonNow = Carbon::now();
    $phpTime = time();
    
    echo "   Laravel now(): {$now}\n";
    echo "   Carbon::now(): {$carbonNow}\n";
    echo "   PHP time(): " . date('Y-m-d H:i:s', $phpTime) . "\n";
    echo "   Timezone: " . config('app.timezone') . "\n";
    echo "   PHP timezone: " . date_default_timezone_get() . "\n";

    // 2. Создаем время истечения
    echo "\n2️⃣ Создание времени истечения...\n";
    $duration = 600; // 10 минут
    $expiresAt = now()->addSeconds($duration);
    
    echo "   Длительность: {$duration} секунд\n";
    echo "   Время истечения: {$expiresAt}\n";
    echo "   Время до истечения: " . $expiresAt->diffInSeconds(now()) . " секунд\n";
    echo "   Истекло ли: " . ($expiresAt < now() ? 'ДА' : 'НЕТ') . "\n";

    // 3. Проверяем создание MineMark
    echo "\n3️⃣ Тестовое создание MineMark...\n";
    
    $user = \App\Models\User::find(7);
    $mineLocation = \App\Models\MineLocation::where('is_active', true)->first();
    
    if ($user && $mineLocation) {
        // Очищаем старые метки
        \App\Models\MineMark::where('player_id', $user->id)->delete();
        
        // Создаем новую метку напрямую
        $mark = \App\Models\MineMark::create([
            'player_id' => $user->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'expires_at' => now()->addSeconds(600),
            'is_active' => true,
            'last_attack_at' => null,
            'attack_count' => 0,
        ]);
        
        echo "   Метка создана (ID: {$mark->id})\n";
        echo "   Время создания: {$mark->created_at}\n";
        echo "   Время истечения: {$mark->expires_at}\n";
        echo "   Время до истечения: " . $mark->expires_at->diffInSeconds(now()) . " секунд\n";
        echo "   Активна: " . ($mark->isActive() ? 'ДА' : 'НЕТ') . "\n";
        
        // 4. Проверяем условие очистки
        echo "\n4️⃣ Проверка условий очистки...\n";
        $isExpired = $mark->expires_at < now();
        $isInactive = !$mark->is_active;
        
        echo "   expires_at < now(): " . ($isExpired ? 'ДА' : 'НЕТ') . "\n";
        echo "   is_active = false: " . ($isInactive ? 'ДА' : 'НЕТ') . "\n";
        echo "   Будет удалена при cleanupExpired: " . (($isExpired || $isInactive) ? 'ДА' : 'НЕТ') . "\n";
        
        // 5. Тестируем cleanupExpired
        echo "\n5️⃣ Тест cleanupExpired...\n";
        $countBefore = \App\Models\MineMark::count();
        echo "   Меток до очистки: {$countBefore}\n";
        
        $cleaned = \App\Models\MineMark::cleanupExpired();
        echo "   Очищено меток: {$cleaned}\n";
        
        $countAfter = \App\Models\MineMark::count();
        echo "   Меток после очистки: {$countAfter}\n";
        
        if ($countAfter < $countBefore) {
            echo "   ❌ Метка была удалена при очистке!\n";
        } else {
            echo "   ✅ Метка НЕ была удалена\n";
        }
    } else {
        echo "   ❌ Не найден пользователь или локация\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
