<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Services\BattleLogService;
use App\Services\LogFormattingService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ДИАГНОСТИКА ЖУРНАЛА БОЕВ В РУДНИКАХ ===\n";
    
    // 1. Подготовка
    echo "\n1️⃣ Подготовка данных...\n";
    
    $admin = User::where('name', 'admin')->first();
    $mineLocation = MineLocation::where('name', 'Тарнмор')->first();
    $mob = Mob::where('location_id', $mineLocation->location_id)
        ->where('mob_type', 'mine')
        ->first();
    
    echo "   Игрок: {$admin->name} (ID: {$admin->id})\n";
    echo "   Локация: {$mineLocation->name}\n";
    echo "   Моб: {$mob->name} (ID: {$mob->id})\n";
    
    // 2. Тестируем сервисы
    echo "\n2️⃣ Тестирование сервисов...\n";
    
    $battleLogService = app(BattleLogService::class);
    $logFormatter = app(LogFormattingService::class);
    
    echo "   ✅ BattleLogService создан\n";
    echo "   ✅ LogFormattingService создан\n";
    
    // 3. Тестируем форматирование сообщения
    echo "\n3️⃣ Тестирование форматирования...\n";
    
    $damage = 8;
    $logMessage = $logFormatter->formatMineDetectionAttack(
        $mob->name,
        $admin->name,
        $damage,
        $mineLocation->name
    );
    
    echo "   Сформированное сообщение:\n";
    echo "   HTML: {$logMessage}\n";
    echo "   Текст: " . strip_tags($logMessage) . "\n";
    
    // 4. Проверяем ключ журнала
    echo "\n4️⃣ Проверка ключа журнала...\n";
    
    $battleLogKey = "battle_logs:{$admin->id}";
    echo "   Ключ журнала: {$battleLogKey}\n";
    
    // Проверяем текущие логи
    $currentLogs = Redis::lrange($battleLogKey, 0, -1);
    echo "   Текущих логов: " . count($currentLogs) . "\n";
    
    // 5. Добавляем тестовый лог
    echo "\n5️⃣ Добавление тестового лога...\n";
    
    $battleLogService->addLog($battleLogKey, $logMessage, 'danger');
    echo "   ✅ Лог добавлен через BattleLogService\n";
    
    // 6. Проверяем результат
    echo "\n6️⃣ Проверка результата...\n";
    
    $logsAfter = Redis::lrange($battleLogKey, 0, -1);
    echo "   Логов после добавления: " . count($logsAfter) . "\n";
    
    if (count($logsAfter) > count($currentLogs)) {
        echo "   ✅ Лог успешно добавлен!\n";
        
        $newLog = json_decode($logsAfter[0], true);
        echo "   Новый лог:\n";
        echo "     Сообщение: " . strip_tags($newLog['message'] ?? 'неизвестно') . "\n";
        echo "     Тип: " . ($newLog['type'] ?? 'неизвестно') . "\n";
        echo "     Время: " . ($newLog['timestamp'] ?? 'неизвестно') . "\n";
    } else {
        echo "   ❌ Лог НЕ добавлен\n";
        
        // Дополнительная диагностика
        echo "\n   🔍 Дополнительная диагностика:\n";
        
        // Проверяем прямое добавление в Redis
        $testMessage = json_encode([
            'message' => 'Тестовое сообщение',
            'type' => 'test',
            'timestamp' => now()->toISOString()
        ]);
        
        Redis::lpush($battleLogKey, $testMessage);
        echo "     Прямое добавление в Redis выполнено\n";
        
        $directLogs = Redis::lrange($battleLogKey, 0, -1);
        echo "     Логов после прямого добавления: " . count($directLogs) . "\n";
        
        if (count($directLogs) > count($logsAfter)) {
            echo "     ✅ Прямое добавление работает - проблема в BattleLogService\n";
        } else {
            echo "     ❌ Даже прямое добавление не работает - проблема с Redis\n";
        }
    }
    
    // 7. Проверяем метод getLogs
    echo "\n7️⃣ Проверка метода getLogs...\n";
    
    $retrievedLogs = $battleLogService->getLogs($battleLogKey, null, 10);
    echo "   Логов через getLogs(): " . count($retrievedLogs) . "\n";
    
    if (!empty($retrievedLogs)) {
        echo "   Последний лог через getLogs():\n";
        $lastLog = $retrievedLogs[0];
        echo "     Сообщение: " . strip_tags($lastLog['message'] ?? 'неизвестно') . "\n";
        echo "     Тип: " . ($lastLog['type'] ?? 'неизвестно') . "\n";
    }
    
    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
