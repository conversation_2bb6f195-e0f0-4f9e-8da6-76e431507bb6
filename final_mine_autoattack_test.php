<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Jobs\MineAutoAttackJob;
use App\Services\PlayerHealthService;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ФИНАЛЬНЫЙ ТЕСТ СИСТЕМЫ АВТОАТАК В РУДНИКАХ ===\n";
    
    // 1. Подготовка
    echo "\n1️⃣ Подготовка тестовой среды...\n";
    
    $admin = User::where('name', 'admin')->first();
    $mineLocation = MineLocation::where('name', 'Тарнмор')->first();
    $playerHealthService = app(PlayerHealthService::class);
    $battleLogService = app(BattleLogService::class);
    
    echo "   Игрок: {$admin->name} (ID: {$admin->id})\n";
    echo "   Локация: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    
    // 2. Создаем свежую метку
    echo "\n2️⃣ Создание метки 'Замечен'...\n";
    
    MineMark::where('player_id', $admin->id)->delete();
    Redis::del('mine_mob_targets:' . $mineLocation->id);
    
    $mark = MineMark::create([
        'player_id' => $admin->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'is_active' => true,
        'expires_at' => now()->addMinutes(30),
        'last_attack_at' => null,
        'attack_count' => 0
    ]);
    
    $admin->statistics->update(['current_location' => $mineLocation->name]);
    
    echo "   ✅ Метка создана (ID: {$mark->id})\n";
    echo "   ✅ Локация игрока обновлена\n";
    echo "   ✅ Redis очищен\n";
    
    // 3. Состояние до атаки
    echo "\n3️⃣ Состояние до атаки...\n";
    
    $hpBefore = $playerHealthService->getCurrentHP($admin);
    echo "   HP игрока: {$hpBefore}\n";
    
    $battleLogKey = "battle_logs:{$admin->id}";
    $logsBefore = $battleLogService->getLogs($battleLogKey, null, 1);
    $firstLogBefore = !empty($logsBefore) ? strip_tags($logsBefore[0]['message'] ?? '') : 'нет логов';
    echo "   Последний лог: {$firstLogBefore}\n";
    
    // 4. Запуск автоатаки
    echo "\n4️⃣ Запуск системы автоатак...\n";
    
    $job = new MineAutoAttackJob();
    
    $startTime = microtime(true);
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    $endTime = microtime(true);
    
    echo "   ✅ Job выполнен за " . round(($endTime - $startTime) * 1000, 2) . " мс\n";
    
    // 5. Проверка результатов
    echo "\n5️⃣ Проверка результатов...\n";
    
    // HP
    $hpAfter = $playerHealthService->getCurrentHP($admin);
    $hpDamage = $hpBefore - $hpAfter;
    echo "   HP после атаки: {$hpAfter}\n";
    echo "   Урон: " . ($hpDamage > 0 ? "✅ {$hpDamage} HP" : "❌ 0 HP") . "\n";
    
    // Метка
    $mark->refresh();
    $lastAttack = $mark->last_attack_at;
    $attackCount = $mark->attack_count;
    echo "   Последняя атака: " . ($lastAttack ? "✅ {$lastAttack}" : "❌ не обновлено") . "\n";
    echo "   Количество атак: " . ($attackCount > 0 ? "✅ {$attackCount}" : "❌ 0") . "\n";
    
    // Журнал боя
    $logsAfter = $battleLogService->getLogs($battleLogKey, null, 1);
    $firstLogAfter = !empty($logsAfter) ? strip_tags($logsAfter[0]['message'] ?? '') : 'нет логов';
    $logChanged = $firstLogBefore !== $firstLogAfter;
    echo "   Журнал боя: " . ($logChanged ? "✅ обновлен" : "❌ не изменился") . "\n";
    
    if ($logChanged) {
        echo "     Новая запись: {$firstLogAfter}\n";
    }
    
    // Redis назначения
    $redisKey = 'mine_mob_targets:' . $mineLocation->id;
    $assignments = Redis::hgetall($redisKey);
    $hasAssignments = !empty($assignments);
    echo "   Redis назначения: " . ($hasAssignments ? "✅ созданы" : "❌ отсутствуют") . "\n";
    
    if ($hasAssignments) {
        foreach ($assignments as $playerId => $mobIds) {
            $mobs = json_decode($mobIds, true) ?: [];
            echo "     Игрок {$playerId}: " . count($mobs) . " мобов\n";
        }
    }
    
    // 6. Итоговая оценка
    echo "\n6️⃣ ИТОГОВАЯ ОЦЕНКА СИСТЕМЫ:\n";
    
    $checks = [
        'Урон нанесен' => $hpDamage > 0,
        'Метка обновлена' => $lastAttack !== null,
        'Счетчик атак увеличен' => $attackCount > 0,
        'Журнал боя записан' => $logChanged,
        'Redis назначения созданы' => $hasAssignments
    ];
    
    $passedChecks = 0;
    $totalChecks = count($checks);
    
    foreach ($checks as $checkName => $passed) {
        echo "   " . ($passed ? "✅" : "❌") . " {$checkName}\n";
        if ($passed) $passedChecks++;
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    
    if ($passedChecks === $totalChecks) {
        echo "🎉 СИСТЕМА АВТОАТАК МОБОВ В РУДНИКАХ РАБОТАЕТ ПОЛНОСТЬЮ!\n";
        echo "✅ Все компоненты функционируют корректно:\n";
        echo "   • Мобы атакуют игроков с дебафом 'Замечен'\n";
        echo "   • HP игроков уменьшается от атак\n";
        echo "   • Записи добавляются в журнал боя\n";
        echo "   • Время последней атаки обновляется\n";
        echo "   • Система умного распределения работает\n";
        echo "   • Кулдаун между атаками функционирует\n";
    } else {
        echo "⚠️ СИСТЕМА РАБОТАЕТ ЧАСТИЧНО ({$passedChecks}/{$totalChecks})\n";
        echo "Требуется дополнительная диагностика компонентов с ❌\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    
    // 7. Дополнительная информация
    echo "\n7️⃣ Дополнительная информация:\n";
    echo "   Время выполнения: " . round(($endTime - $startTime) * 1000, 2) . " мс\n";
    echo "   Использованная память: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB\n";
    echo "   Текущее время: " . now()->format('Y-m-d H:i:s') . "\n";
    
    echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
