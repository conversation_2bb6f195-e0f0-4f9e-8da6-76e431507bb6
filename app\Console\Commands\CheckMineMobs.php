<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;
use App\Models\MineLocation;

class CheckMineMobs extends Command
{
    protected $signature = 'check:mine-mobs';
    protected $description = 'Проверка мобов в рудниках';

    public function handle()
    {
        $this->info('=== Проверка мобов в рудниках ===');

        // 1. Проверяем общее количество мобов
        $totalMobs = Mob::count();
        $this->info("Всего мобов в базе: {$totalMobs}");

        // 2. Проверяем мобов с mob_type = 'mine'
        $mineTypeMobs = Mob::where('mob_type', 'mine')->count();
        $this->info("Мобов с mob_type = 'mine': {$mineTypeMobs}");

        // 3. Проверяем мобов с mine_location_id
        $mineLocationMobs = Mob::whereNotNull('mine_location_id')->count();
        $this->info("Мобов с mine_location_id: {$mineLocationMobs}");

        // 4. Показываем примеры мобов
        $this->info("\n=== Примеры мобов ===");
        $sampleMobs = Mob::take(10)->get(['id', 'name', 'location_id', 'mine_location_id', 'mob_type', 'hp']);

        foreach ($sampleMobs as $mob) {
            $this->line("ID: {$mob->id}, Name: {$mob->name}, Location: {$mob->location_id}, Mine: {$mob->mine_location_id}, Type: {$mob->mob_type}, HP: {$mob->hp}");
        }

        // 6. Проверяем локации рудников
        $this->info("\n=== Локации рудников ===");
        $mineLocations = MineLocation::take(5)->get(['id', 'name', 'location_id']);

        foreach ($mineLocations as $location) {
            $this->line("ID: {$location->id}, Name: {$location->name}, Location ID: {$location->location_id}");

            // Проверяем мобов в этой локации
            $mobsInLocation = Mob::where('location_id', $location->location_id)->count();
            $mobsInMineLocation = Mob::where('mine_location_id', $location->id)->count();

            $this->line("  - Мобов по location_id: {$mobsInLocation}");
            $this->line("  - Мобов по mine_location_id: {$mobsInMineLocation}");
        }

        // 7. Создаем тестового моба для рудника
        $this->info("\n=== Создание тестового моба ===");
        $testMineLocation = MineLocation::first();

        if ($testMineLocation) {
            $testMob = Mob::create([
                'name' => 'Тестовый рудничный моб',
                'slug' => 'test-mine-mob',
                'hp' => 100,
                'max_hp' => 100,
                'location_id' => $testMineLocation->location_id,
                'mine_location_id' => $testMineLocation->id,
                'mob_type' => 'mine',
                'strength' => 10,
                'defense' => 5,
                'agility' => 8,
                'vitality' => 12,
                'intelligence' => 6,
                'experience_reward' => 50,
                'respawn_time' => 5,
                'description' => 'Тестовый моб для проверки системы автоатак в рудниках'
            ]);

            $this->info("✅ Создан тестовый моб ID: {$testMob->id} в локации {$testMineLocation->name}");

            // Проверяем, что моб найдется в запросе
            $foundMob = Mob::where('location_id', $testMineLocation->location_id)
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->where(function ($query) {
                    $query->whereNull('death_time')
                        ->orWhere('death_time', '<', now()->subMinutes(5));
                })
                ->first();

            if ($foundMob) {
                $this->info("✅ Тестовый моб найден в запросе автоатак");
            } else {
                $this->error("❌ Тестовый моб НЕ найден в запросе автоатак");
            }
        } else {
            $this->error("❌ Не найдена локация рудника для создания тестового моба");
        }

        $this->info("\n=== Проверка завершена ===");
    }
}
