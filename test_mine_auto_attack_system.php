<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТ СИСТЕМЫ АВТОАТАК В РУДНИКАХ ===\n";

try {
    // 1. Очищаем старые метки
    echo "🧹 Очистка старых меток...\n";
    MineMark::where('player_id', 7)->delete();
    \App\Models\ActiveEffect::where('target_type', 'player')
        ->where('target_id', 7)
        ->where('effect_type', 'mine_detection')
        ->delete();

    // 2. Находим пользователя и локацию
    $user = User::find(7); // admin
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    if (!$user || !$mineLocation) {
        echo "❌ Не найден пользователь или локация рудника\n";
        exit(1);
    }
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";

    // 3. Проверяем мобов в рудниках
    $mineMobs = Mob::whereNotNull('mine_location_id')->where('hp', '>', 0)->get();
    echo "✅ Доступных мобов в рудниках: {$mineMobs->count()}\n";
    
    if ($mineMobs->count() == 0) {
        echo "❌ Нет мобов для атаки\n";
        exit(1);
    }
    
    foreach ($mineMobs as $mob) {
        echo "   - {$mob->name} (ID: {$mob->id}) HP: {$mob->hp}/{$mob->max_hp} Локация: {$mob->location_id} Рудник: {$mob->mine_location_id}\n";
    }

    // 4. Создаем дебаф с увеличенным временем (10 минут)
    echo "\n🎯 Создание дебафа 'Замечен' на 10 минут...\n";
    $mineDetectionService = app(MineDetectionService::class);
    
    $mark = $mineDetectionService->createMark($user, $mineLocation, 600); // 10 минут
    
    if (!$mark) {
        echo "❌ Не удалось создать метку\n";
        exit(1);
    }
    
    echo "✅ Метка создана!\n";
    echo "   ID: {$mark->id}\n";
    echo "   Истекает: {$mark->expires_at}\n";
    echo "   Время до истечения: " . $mark->expires_at->diffInSeconds(now()) . " секунд\n";

    // 5. Создаем ActiveEffect для отображения
    $effect = \App\Models\ActiveEffect::create([
        'effect_type' => 'mine_detection',
        'effect_name' => 'Замечен в рудниках',
        'target_type' => 'player',
        'target_id' => $user->id,
        'duration' => 600,
        'starts_at' => now(),
        'ends_at' => $mark->expires_at,
        'is_active' => true,
        'effect_data' => [
            'mine_mark_id' => $mark->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'description' => "Вы были замечены при добыче ресурсов в {$mineLocation->name}. Мобы могут атаковать вас!"
        ]
    ]);
    
    echo "✅ ActiveEffect создан (ID: {$effect->id})\n";

    // 6. Проверяем HP игрока до атаки
    $playerHealthService = app(\App\Services\PlayerHealthService::class);
    $hpBefore = $playerHealthService->getCurrentHP($user);
    echo "\n💚 HP игрока до атаки: {$hpBefore}\n";

    // 7. Запускаем автоатаку
    echo "\n🚀 Запуск автоатаки мобов...\n";
    
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "✅ MineAutoAttackJob выполнен\n";

    // 8. Проверяем результаты
    echo "\n📊 Проверка результатов...\n";
    
    // Обновляем метку из БД
    $markAfter = MineMark::find($mark->id);
    if ($markAfter) {
        echo "✅ Метка найдена после атаки\n";
        echo "   Последняя атака: " . ($markAfter->last_attack_at ? $markAfter->last_attack_at : 'НЕТ') . "\n";
        echo "   Количество атак: {$markAfter->attack_count}\n";
        
        if ($markAfter->last_attack_at) {
            echo "   🎯 АТАКА ПРОИЗОШЛА!\n";
        } else {
            echo "   ❌ Атака НЕ произошла\n";
        }
    } else {
        echo "❌ Метка не найдена после атаки\n";
    }
    
    // Проверяем HP игрока после атаки
    $hpAfter = $playerHealthService->getCurrentHP($user);
    echo "💚 HP игрока после атаки: {$hpAfter}\n";
    
    if ($hpBefore > $hpAfter) {
        echo "🎯 УРОН НАНЕСЕН! Потеряно HP: " . ($hpBefore - $hpAfter) . "\n";
    } else {
        echo "❌ Урон НЕ нанесен\n";
    }

    // 9. Проверяем журнал боя
    echo "\n📜 Проверка журнала боя...\n";
    $battleLogService = app(\App\Services\BattleLogService::class);
    $battleLogs = $battleLogService->getLogs("battle_logs:{$user->id}", null, 5);
    
    if (!empty($battleLogs)) {
        echo "✅ Найдено записей в журнале: " . count($battleLogs) . "\n";
        foreach ($battleLogs as $log) {
            if (is_array($log) && isset($log['message'])) {
                $message = strip_tags($log['message']);
                if (strlen($message) > 100) {
                    $message = substr($message, 0, 100) . '...';
                }
                echo "   - {$message}\n";
            }
        }
    } else {
        echo "❌ Записи в журнале боя не найдены\n";
    }

    echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
