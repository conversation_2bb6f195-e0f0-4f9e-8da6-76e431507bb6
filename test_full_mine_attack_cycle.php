<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Jobs\MineAutoAttackJob;
use App\Services\PlayerHealthService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ПОЛНЫЙ ТЕСТ ЦИКЛА АВТОАТАК В РУДНИКАХ ===\n";
    
    // 1. Подготовка
    echo "\n1️⃣ Подготовка данных...\n";
    
    $admin = User::where('name', 'admin')->first();
    $mineLocation = MineLocation::where('name', 'Тарнмор')->first();
    
    echo "   Игрок: {$admin->name} (ID: {$admin->id})\n";
    echo "   Локация: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    
    // 2. Проверяем текущее состояние
    echo "\n2️⃣ Текущее состояние...\n";
    
    $playerHealthService = app(PlayerHealthService::class);
    $hpBefore = $playerHealthService->getCurrentHP($admin);
    echo "   HP игрока до атаки: {$hpBefore}\n";
    
    // 3. Проверяем метки
    echo "\n3️⃣ Проверка меток...\n";
    
    $activeMark = MineMark::where('player_id', $admin->id)
        ->where('mine_location_id', $mineLocation->id)
        ->where('is_active', true)
        ->where('expires_at', '>', now())
        ->first();
    
    if ($activeMark) {
        echo "   ✅ Активная метка найдена (ID: {$activeMark->id})\n";
        echo "   Последняя атака: " . ($activeMark->last_attack_at ?: 'никогда') . "\n";
        echo "   Количество атак: {$activeMark->attack_count}\n";
        
        // Сбрасываем кулдаун для теста
        $activeMark->update(['last_attack_at' => null]);
        echo "   ✅ Кулдаун сброшен для теста\n";
    } else {
        echo "   ❌ Активная метка не найдена\n";
        exit(1);
    }
    
    // 4. Очищаем Redis назначения
    echo "\n4️⃣ Очистка Redis назначений...\n";
    
    $redisKey = 'mine_mob_targets:' . $mineLocation->id;
    Redis::del($redisKey);
    echo "   ✅ Redis очищен\n";
    
    // 5. Запускаем Job
    echo "\n5️⃣ Запуск MineAutoAttackJob...\n";
    
    $job = new MineAutoAttackJob();
    
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "   ✅ Job выполнен\n";
    
    // 6. Проверяем результаты
    echo "\n6️⃣ Проверка результатов...\n";
    
    // Обновляем данные игрока
    $admin->refresh();
    $admin->profile->refresh();
    
    $hpAfter = $playerHealthService->getCurrentHP($admin);
    echo "   HP игрока после атаки: {$hpAfter}\n";
    
    if ($hpBefore > $hpAfter) {
        echo "   🎯 УРОН НАНЕСЕН! Потеряно HP: " . ($hpBefore - $hpAfter) . "\n";
    } else {
        echo "   ❌ Урон НЕ нанесен\n";
    }
    
    // Проверяем обновление метки
    $activeMark->refresh();
    if ($activeMark->last_attack_at) {
        echo "   ✅ Время последней атаки обновлено: {$activeMark->last_attack_at}\n";
        echo "   ✅ Количество атак: {$activeMark->attack_count}\n";
    } else {
        echo "   ❌ Время последней атаки НЕ обновлено\n";
    }
    
    // 7. Проверяем журнал боя
    echo "\n7️⃣ Проверка журнала боя...\n";
    
    $battleLogKey = "battle_logs:{$admin->id}";
    $battleLogs = Redis::lrange($battleLogKey, 0, 4); // Последние 5 записей
    
    echo "   Записей в журнале боя: " . count($battleLogs) . "\n";
    
    if (!empty($battleLogs)) {
        echo "   Последние записи:\n";
        foreach (array_slice($battleLogs, 0, 3) as $index => $log) {
            $logData = json_decode($log, true);
            echo "     " . ($index + 1) . ". " . ($logData['message'] ?? 'Неизвестное сообщение') . "\n";
        }
    }
    
    // 8. Проверяем Redis назначения
    echo "\n8️⃣ Проверка Redis назначений...\n";
    
    $assignments = Redis::hgetall($redisKey);
    echo "   Назначений в Redis: " . count($assignments) . "\n";
    
    foreach ($assignments as $playerId => $mobIds) {
        $mobs = json_decode($mobIds, true) ?: [];
        echo "     Игрок {$playerId}: " . count($mobs) . " мобов\n";
    }
    
    echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
    
    // Итоговая оценка
    $success = ($hpBefore > $hpAfter) && $activeMark->last_attack_at && !empty($battleLogs);
    
    if ($success) {
        echo "🎉 СИСТЕМА АВТОАТАК РАБОТАЕТ ПОЛНОСТЬЮ!\n";
        echo "✅ Урон нанесен\n";
        echo "✅ Метка обновлена\n";
        echo "✅ Журнал боя записан\n";
    } else {
        echo "❌ СИСТЕМА АВТОАТАК РАБОТАЕТ ЧАСТИЧНО\n";
        echo "Урон: " . ($hpBefore > $hpAfter ? '✅' : '❌') . "\n";
        echo "Метка: " . ($activeMark->last_attack_at ? '✅' : '❌') . "\n";
        echo "Журнал: " . (!empty($battleLogs) ? '✅' : '❌') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
