<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Redis;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТ АВТОАТАК С УЧЕТОМ КУЛДАУНА ===\n";

try {
    $user = User::find(7);
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";

    // 1. Очищаем все старые метки
    echo "\n🧹 Очистка старых меток...\n";
    MineMark::where('player_id', $user->id)->delete();
    \App\Models\ActiveEffect::where('target_type', 'player')
        ->where('target_id', $user->id)
        ->where('effect_type', 'mine_detection')
        ->delete();

    // 2. Устанавливаем правильную локацию игрока
    echo "\n📍 Установка локации игрока...\n";
    $user->statistics->current_location = $mineLocation->name;
    $user->statistics->save();
    echo "   Локация установлена: {$mineLocation->name}\n";

    // 3. Создаем новую метку БЕЗ времени последней атаки
    echo "\n🎯 Создание новой метки...\n";
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null, // Важно: НЕТ времени последней атаки
        'attack_count' => 0,
    ]);
    
    echo "   Метка создана (ID: {$mark->id})\n";
    echo "   Время последней атаки: " . ($mark->last_attack_at ? $mark->last_attack_at : 'НЕТ') . "\n";

    // 4. Создаем ActiveEffect для отображения
    $effect = \App\Models\ActiveEffect::create([
        'effect_type' => 'mine_detection',
        'effect_name' => 'Замечен в рудниках',
        'target_type' => 'player',
        'target_id' => $user->id,
        'duration' => 600,
        'starts_at' => now(),
        'ends_at' => $mark->expires_at,
        'is_active' => true,
        'effect_data' => [
            'mine_mark_id' => $mark->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'description' => "Вы были замечены при добыче ресурсов в {$mineLocation->name}. Мобы могут атаковать вас!"
        ]
    ]);
    
    echo "   ActiveEffect создан (ID: {$effect->id})\n";

    // 5. Проверяем мобов
    echo "\n🐉 Проверка мобов в руднике...\n";
    $availableMobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mine_location_id', $mineLocation->id)
        ->where('hp', '>', 0)
        ->get();
    
    echo "   Доступных мобов: {$availableMobs->count()}\n";
    foreach ($availableMobs as $mob) {
        echo "     - {$mob->name} (ID: {$mob->id}) HP: {$mob->hp}/{$mob->max_hp}\n";
    }

    // 6. Проверяем HP игрока до атаки
    $playerHealthService = app(\App\Services\PlayerHealthService::class);
    $hpBefore = $playerHealthService->getCurrentHP($user);
    echo "\n💚 HP игрока до атаки: {$hpBefore}\n";

    // 7. Проверяем журнал боя до атаки
    $battleLogService = app(\App\Services\BattleLogService::class);
    $battleLogKey = "battle_logs:{$user->id}";
    $logsBefore = Redis::llen("{$battleLogKey}:logs");
    echo "📜 Записей в журнале до атаки: {$logsBefore}\n";

    // 8. Запускаем автоатаку
    echo "\n🚀 Запуск автоатаки мобов...\n";
    
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "   ✅ MineAutoAttackJob выполнен\n";

    // 9. Проверяем результаты
    echo "\n📊 Проверка результатов...\n";
    
    // Обновляем метку из БД
    $markAfter = MineMark::find($mark->id);
    if ($markAfter) {
        echo "   ✅ Метка найдена после атаки\n";
        echo "   Последняя атака: " . ($markAfter->last_attack_at ? $markAfter->last_attack_at : 'НЕТ') . "\n";
        echo "   Количество атак: {$markAfter->attack_count}\n";
        
        if ($markAfter->last_attack_at) {
            echo "   🎯 АТАКА ПРОИЗОШЛА!\n";
        } else {
            echo "   ❌ Атака НЕ произошла\n";
        }
    } else {
        echo "   ❌ Метка не найдена после атаки (возможно удалена)\n";
    }
    
    // Проверяем HP игрока после атаки
    $hpAfter = $playerHealthService->getCurrentHP($user);
    echo "   💚 HP игрока после атаки: {$hpAfter}\n";
    
    if ($hpBefore > $hpAfter) {
        echo "   🎯 УРОН НАНЕСЕН! Потеряно HP: " . ($hpBefore - $hpAfter) . "\n";
    } else {
        echo "   ❌ Урон НЕ нанесен\n";
    }

    // Проверяем журнал боя после атаки
    $logsAfter = Redis::llen("{$battleLogKey}:logs");
    echo "   📜 Записей в журнале после атаки: {$logsAfter}\n";
    
    if ($logsAfter > $logsBefore) {
        echo "   🎯 ЗАПИСЬ В ЖУРНАЛ ДОБАВЛЕНА!\n";
        
        // Показываем новую запись
        $newEntry = Redis::lindex("{$battleLogKey}:logs", 0);
        $newEntryData = json_decode($newEntry, true);
        if ($newEntryData && isset($newEntryData['message'])) {
            $message = strip_tags($newEntryData['message']);
            echo "   Новая запись: {$message}\n";
        }
    } else {
        echo "   ❌ Запись в журнал НЕ добавлена\n";
    }

    // 10. Итоговый статус
    echo "\n=== ИТОГОВЫЙ СТАТУС ===\n";
    
    $attackSuccess = $markAfter && $markAfter->last_attack_at;
    $damageSuccess = $hpBefore > $hpAfter;
    $logSuccess = $logsAfter > $logsBefore;
    
    if ($attackSuccess && $damageSuccess && $logSuccess) {
        echo "🎉 ВСЕ СИСТЕМЫ РАБОТАЮТ КОРРЕКТНО!\n";
        echo "   ✅ Автоатака выполнена\n";
        echo "   ✅ Урон нанесен\n";
        echo "   ✅ Запись в журнал добавлена\n";
    } else {
        echo "💥 ЕСТЬ ПРОБЛЕМЫ:\n";
        echo "   " . ($attackSuccess ? "✅" : "❌") . " Автоатака\n";
        echo "   " . ($damageSuccess ? "✅" : "❌") . " Нанесение урона\n";
        echo "   " . ($logSuccess ? "✅" : "❌") . " Запись в журнал\n";
    }

    // 11. Тест повторной атаки (должна быть заблокирована кулдауном)
    echo "\n⏰ Тест кулдауна (повторная атака)...\n";
    
    if ($attackSuccess) {
        echo "   Запуск повторной атаки (должна быть заблокирована)...\n";
        
        $job2 = new \App\Jobs\MineAutoAttackJob();
        $job2->handle(
            app(\App\Services\MineDetectionService::class),
            app(\App\Services\MineTargetDistributionService::class),
            app(\App\Services\BattleLogService::class),
            app(\App\Services\PlayerHealthService::class),
            app(\App\Services\CombatFormulaService::class),
            app(\App\Services\LogFormattingService::class)
        );
        
        $markAfter2 = MineMark::find($mark->id);
        if ($markAfter2 && $markAfter2->attack_count == $markAfter->attack_count) {
            echo "   ✅ Кулдаун работает - повторная атака заблокирована\n";
        } else {
            echo "   ❌ Кулдаун НЕ работает - произошла повторная атака\n";
        }
    }

    echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
