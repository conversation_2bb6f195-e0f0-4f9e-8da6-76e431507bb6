<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MineMark;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

class DebugMineMarkValidation extends Command
{
    protected $signature = 'debug:mine-mark-validation';
    protected $description = 'Отладка валидации меток в MineAutoAttackJob';

    public function handle()
    {
        $this->info('=== Отладка валидации меток в MineAutoAttackJob ===');
        
        // 1. Создаем тестовую метку
        $testUser = User::first();
        $testMineLocation = MineLocation::first();
        
        if (!$testUser || !$testMineLocation) {
            $this->error('❌ Не найден тестовый пользователь или локация рудника');
            return;
        }
        
        $mineDetectionService = app(MineDetectionService::class);
        $mark = $mineDetectionService->applyDetectionDebuff($testUser, $testMineLocation);
        
        if (!$mark) {
            $this->error('❌ Не удалось создать метку обнаружения');
            return;
        }
        
        $this->info("✅ Метка создана (ID: {$mark->id})");
        
        // 2. Проверяем все метки в базе
        $this->info("\n=== Все метки в базе ===");
        $allMarks = MineMark::with(['player.profile', 'mineLocation'])
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->get();
            
        $this->info("Найдено активных меток: " . $allMarks->count());
        
        foreach ($allMarks as $mark) {
            $this->line("\n--- Метка ID: {$mark->id} ---");
            $this->line("Player ID: {$mark->player_id}");
            $this->line("Mine Location ID: {$mark->mine_location_id}");
            $this->line("Location ID: {$mark->location_id}");
            $this->line("Location Name: {$mark->location_name}");
            $this->line("Expires At: {$mark->expires_at}");
            
            // Проверяем связи
            $this->line("\n--- Проверка связей ---");
            
            if (!$mark->player) {
                $this->error("❌ Нет связи с игроком");
                continue;
            } else {
                $this->info("✅ Игрок найден: {$mark->player->name}");
            }
            
            if (!$mark->player->profile) {
                $this->error("❌ У игрока нет профиля");
                continue;
            } else {
                $this->info("✅ Профиль найден, HP: {$mark->player->profile->current_hp}");
            }
            
            if (!$mark->mineLocation) {
                $this->error("❌ Нет связи с локацией рудника");
                continue;
            } else {
                $this->info("✅ Локация рудника найдена: {$mark->mineLocation->name}");
            }
            
            if ($mark->player->profile->current_hp <= 0) {
                $this->warn("⚠️ Игрок мертв (HP <= 0)");
                continue;
            }
            
            // Проверяем локацию игрока
            $this->line("\n--- Проверка локации игрока ---");
            
            if (!$mark->player->statistics) {
                $this->error("❌ У игрока нет статистики");
                continue;
            } else {
                $playerLocation = $mark->player->statistics->current_location;
                $this->info("Текущая локация игрока: '{$playerLocation}'");
                $this->info("Ожидаемая локация: '{$mark->location_name}'");
                
                if ($playerLocation !== $mark->location_name) {
                    $this->error("❌ Игрок не в той локации!");
                    continue;
                } else {
                    $this->info("✅ Игрок в правильной локации");
                }
            }
            
            $this->info("✅ Метка прошла все проверки - должна быть обработана");
        }
        
        // 3. Симулируем логику getActiveMarkedPlayers
        $this->info("\n=== Симуляция getActiveMarkedPlayers ===");
        
        $validPlayers = [];
        
        foreach ($allMarks as $mark) {
            // Проверки валидности (как в оригинальном коде)
            if (
                !$mark->player ||
                !$mark->player->profile ||
                !$mark->mineLocation ||
                $mark->player->profile->current_hp <= 0
            ) {
                $this->warn("Метка {$mark->id} не прошла базовые проверки");
                continue;
            }

            // Проверяем, что игрок находится в нужной локации
            $playerLocation = $mark->player->statistics ? $mark->player->statistics->current_location : null;
            if ($playerLocation !== $mark->location_name) {
                $this->warn("Метка {$mark->id} не прошла проверку локации: '{$playerLocation}' != '{$mark->location_name}'");
                continue;
            }

            $validPlayers[] = [
                'mark_id' => $mark->id,
                'player_id' => $mark->player_id,
                'player' => $mark->player,
                'mine_location_id' => $mark->mine_location_id,
                'mine_location' => $mark->mineLocation,
                'location_id' => $mark->location_id,
                'location_name' => $mark->location_name,
                'expires_at' => $mark->expires_at,
                'last_attack_at' => $mark->last_attack_at,
                'attack_count' => $mark->attack_count ?? 0
            ];
        }
        
        $this->info("Валидных игроков для атаки: " . count($validPlayers));
        
        // 4. Очистка
        $mark->delete();
        $this->info("\n🧹 Тестовая метка удалена");
        
        $this->info("\n=== Отладка завершена ===");
    }
}
