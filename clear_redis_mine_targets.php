<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ОЧИСТКА REDIS НАЗНАЧЕНИЙ МОБОВ ===\n";
    
    // Очищаем назначения для локации Тарнмор (ID: 71)
    $redisKey = 'mine_mob_targets:71';
    $deleted = Redis::del($redisKey);
    
    echo "Ключ: {$redisKey}\n";
    echo "Удалено: " . ($deleted ? 'ДА' : 'НЕТ') . "\n";
    
    // Проверяем что очистилось
    $assignments = Redis::hgetall($redisKey);
    echo "Назначений после очистки: " . count($assignments) . "\n";
    
    echo "✅ Redis очищен успешно\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
}
