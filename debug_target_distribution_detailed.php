<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДЕТАЛЬНАЯ ДИАГНОСТИКА MineTargetDistributionService ===\n";

try {
    $user = User::find(7);
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";

    // 1. Очищаем старые метки и создаем новую
    echo "\n1️⃣ Подготовка данных...\n";
    MineMark::where('player_id', $user->id)->delete();
    
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null,
        'attack_count' => 0,
    ]);
    
    $user->statistics->current_location = $mineLocation->name;
    $user->statistics->save();
    
    echo "   Метка создана (ID: {$mark->id})\n";
    echo "   Локация игрока: {$mineLocation->name}\n";

    // 2. Получаем замеченных игроков как в MineAutoAttackJob
    echo "\n2️⃣ Получение замеченных игроков...\n";
    $mineDetectionService = app(MineDetectionService::class);
    
    // Симулируем getActiveMarkedPlayers
    $marks = MineMark::with(['player.profile', 'mineLocation'])
        ->where('is_active', true)
        ->where('expires_at', '>', now())
        ->get();
    
    echo "   Найдено меток в БД: {$marks->count()}\n";
    
    $markedPlayers = [];
    foreach ($marks as $mark) {
        if (!$mark->player || !$mark->player->profile || !$mark->mineLocation) {
            continue;
        }
        
        if ($mark->player->profile->current_hp <= 0) {
            continue;
        }
        
        if (!$mark->player->statistics) {
            continue;
        }
        
        if ($mark->player->statistics->current_location !== $mark->location_name) {
            continue;
        }
        
        $markedPlayers[] = [
            'mark_id' => $mark->id,
            'player_id' => $mark->player_id,
            'player' => $mark->player,
            'mine_location_id' => $mark->mine_location_id,
            'mine_location' => $mark->mineLocation,
            'location_id' => $mark->location_id,
            'location_name' => $mark->location_name,
            'expires_at' => $mark->expires_at,
            'last_attack_at' => $mark->last_attack_at,
            'attack_count' => $mark->attack_count ?? 0
        ];
    }
    
    echo "   Валидных замеченных игроков: " . count($markedPlayers) . "\n";
    
    if (empty($markedPlayers)) {
        echo "   ❌ Нет валидных замеченных игроков\n";
        exit(1);
    }

    // 3. Группируем игроков по локациям
    echo "\n3️⃣ Группировка игроков по локациям...\n";
    $playersByLocation = [];
    foreach ($markedPlayers as $playerData) {
        $mineLocationId = $playerData['mine_location_id'];
        if (!isset($playersByLocation[$mineLocationId])) {
            $playersByLocation[$mineLocationId] = [];
        }
        $playersByLocation[$mineLocationId][] = $playerData;
    }
    
    echo "   Локаций с игроками: " . count($playersByLocation) . "\n";
    foreach ($playersByLocation as $locationId => $players) {
        echo "     - Локация {$locationId}: " . count($players) . " игроков\n";
    }

    // 4. Тестируем MineTargetDistributionService
    echo "\n4️⃣ Тестирование MineTargetDistributionService...\n";
    $targetDistributionService = app(MineTargetDistributionService::class);
    
    foreach ($playersByLocation as $mineLocationId => $locationPlayers) {
        echo "\n   Обработка локации {$mineLocationId}...\n";
        
        $mineLocationObj = $locationPlayers[0]['mine_location'];
        echo "     Название локации: {$mineLocationObj->name}\n";
        
        // Очищаем истекшие назначения
        $targetDistributionService->cleanupExpiredAssignments($mineLocationObj);
        echo "     ✅ Очистка истекших назначений выполнена\n";
        
        // Получаем оптимальную пару
        echo "     Поиск оптимальной пары моб-игрок...\n";
        
        $mobPlayerPair = $targetDistributionService->getOptimalMobPlayerPair(
            collect($locationPlayers),
            $mineLocationObj
        );
        
        if ($mobPlayerPair) {
            echo "     ✅ ПАРА НАЙДЕНА!\n";
            echo "       Моб: {$mobPlayerPair['mob']->name} (ID: {$mobPlayerPair['mob']->id})\n";
            echo "       Игрок: {$mobPlayerPair['player']->name} (ID: {$mobPlayerPair['player']->id})\n";
            
            // Тестируем executeSmartAttack
            echo "\n     Тестирование executeSmartAttack...\n";
            
            $playerHealthService = app(\App\Services\PlayerHealthService::class);
            $hpBefore = $playerHealthService->getCurrentHP($mobPlayerPair['player']);
            echo "       HP игрока до атаки: {$hpBefore}\n";
            
            // Создаем рефлексию для доступа к приватному методу
            $jobReflection = new ReflectionClass(\App\Jobs\MineAutoAttackJob::class);
            $executeSmartAttackMethod = $jobReflection->getMethod('executeSmartAttack');
            $executeSmartAttackMethod->setAccessible(true);
            
            $job = new \App\Jobs\MineAutoAttackJob();
            
            try {
                $result = $executeSmartAttackMethod->invoke(
                    $job,
                    $mobPlayerPair['mob'],
                    $mobPlayerPair['player'],
                    $mineLocationObj,
                    app(\App\Services\MineDetectionService::class),
                    app(\App\Services\BattleLogService::class),
                    app(\App\Services\PlayerHealthService::class),
                    app(\App\Services\CombatFormulaService::class),
                    app(\App\Services\LogFormattingService::class)
                );
                
                echo "       ✅ executeSmartAttack выполнен, результат: " . ($result ? 'true' : 'false') . "\n";
                
                $hpAfter = $playerHealthService->getCurrentHP($mobPlayerPair['player']);
                echo "       HP игрока после атаки: {$hpAfter}\n";
                
                if ($hpBefore > $hpAfter) {
                    echo "       🎯 УРОН НАНЕСЕН! Потеряно HP: " . ($hpBefore - $hpAfter) . "\n";
                } else {
                    echo "       ❌ Урон НЕ нанесен\n";
                }
                
                // Проверяем обновление метки
                $markAfter = MineMark::find($mark->id);
                if ($markAfter && $markAfter->last_attack_at) {
                    echo "       ✅ Время последней атаки обновлено: {$markAfter->last_attack_at}\n";
                } else {
                    echo "       ❌ Время последней атаки НЕ обновлено\n";
                }
                
            } catch (\Exception $e) {
                echo "       ❌ Ошибка в executeSmartAttack: " . $e->getMessage() . "\n";
            }
            
        } else {
            echo "     ❌ ПАРА НЕ НАЙДЕНА\n";
            
            // Детальная диагностика почему пара не найдена
            echo "\n     Детальная диагностика...\n";
            
            // Проверяем доступных мобов
            $availableMobs = Mob::where('location_id', $mineLocationObj->location_id)
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->where(function ($query) {
                    $query->whereNull('death_time')
                        ->orWhere('death_time', '<', now()->subMinutes(5));
                })
                ->get();
            
            echo "       Доступных мобов: {$availableMobs->count()}\n";
            foreach ($availableMobs as $mob) {
                echo "         - {$mob->name} (ID: {$mob->id})\n";
            }
            
            // Проверяем игроков после фильтра кулдауна
            $playersCollection = collect($locationPlayers);
            $availablePlayers = $playersCollection->filter(function ($playerData) {
                $lastAttackAt = $playerData['last_attack_at'] ?? null;
                
                if (!$lastAttackAt) {
                    return true;
                }
                
                $cooldownEnd = \Carbon\Carbon::parse($lastAttackAt)->addSeconds(25);
                return $cooldownEnd->isPast();
            });
            
            echo "       Игроков после фильтра кулдауна: {$availablePlayers->count()}\n";
            foreach ($availablePlayers as $playerData) {
                echo "         - {$playerData['player']->name} (ID: {$playerData['player']->id})\n";
            }
        }
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
