<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Jobs\MineAutoAttackJob;
use App\Services\PlayerHealthService;
use Illuminate\Support\Facades\Redis;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== СОЗДАНИЕ МЕТКИ И ТЕСТ АВТОАТАК ===\n";
    
    // 1. Подготовка
    echo "\n1️⃣ Подготовка данных...\n";
    
    $admin = User::where('name', 'admin')->first();
    $mineLocation = MineLocation::where('name', 'Тарнмор')->first();
    
    echo "   Игрок: {$admin->name} (ID: {$admin->id})\n";
    echo "   Локация: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    
    // 2. Создаем метку
    echo "\n2️⃣ Создание метки...\n";
    
    // Удаляем старые метки
    MineMark::where('player_id', $admin->id)->delete();
    
    $mark = MineMark::create([
        'player_id' => $admin->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'is_active' => true,
        'expires_at' => now()->addMinutes(30),
        'last_attack_at' => null,
        'attack_count' => 0
    ]);
    
    echo "   ✅ Метка создана (ID: {$mark->id})\n";
    
    // Обновляем локацию игрока
    $admin->statistics->update(['current_location' => $mineLocation->name]);
    echo "   ✅ Локация игрока обновлена\n";
    
    // 3. Очищаем Redis
    echo "\n3️⃣ Очистка Redis...\n";
    
    $redisKey = 'mine_mob_targets:' . $mineLocation->id;
    Redis::del($redisKey);
    echo "   ✅ Redis очищен\n";
    
    // 4. Проверяем HP до атаки
    echo "\n4️⃣ Состояние до атаки...\n";
    
    $playerHealthService = app(PlayerHealthService::class);
    $hpBefore = $playerHealthService->getCurrentHP($admin);
    echo "   HP игрока: {$hpBefore}\n";
    
    // 5. Запускаем Job
    echo "\n5️⃣ Запуск MineAutoAttackJob...\n";
    
    $job = new MineAutoAttackJob();
    
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "   ✅ Job выполнен\n";
    
    // 6. Проверяем результаты
    echo "\n6️⃣ Результаты...\n";
    
    $hpAfter = $playerHealthService->getCurrentHP($admin);
    echo "   HP после атаки: {$hpAfter}\n";
    
    $mark->refresh();
    echo "   Последняя атака: " . ($mark->last_attack_at ?: 'не обновлено') . "\n";
    echo "   Количество атак: {$mark->attack_count}\n";
    
    // Проверяем журнал боя
    $battleLogKey = "battle_logs:{$admin->id}";
    $battleLogs = Redis::lrange($battleLogKey, 0, 2);
    echo "   Записей в журнале: " . count($battleLogs) . "\n";
    
    if (!empty($battleLogs)) {
        $lastLog = json_decode($battleLogs[0], true);
        echo "   Последняя запись: " . ($lastLog['message'] ?? 'неизвестно') . "\n";
    }
    
    // Итог
    $success = ($hpBefore > $hpAfter) && $mark->last_attack_at && !empty($battleLogs);
    
    echo "\n=== РЕЗУЛЬТАТ ===\n";
    if ($success) {
        echo "🎉 ПОЛНЫЙ УСПЕХ! Система автоатак работает!\n";
        echo "✅ Урон: " . ($hpBefore - $hpAfter) . " HP\n";
        echo "✅ Метка обновлена\n";
        echo "✅ Журнал записан\n";
    } else {
        echo "❌ Проблемы в системе:\n";
        echo "Урон: " . ($hpBefore > $hpAfter ? '✅' : '❌') . "\n";
        echo "Метка: " . ($mark->last_attack_at ? '✅' : '❌') . "\n";
        echo "Журнал: " . (!empty($battleLogs) ? '✅' : '❌') . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
