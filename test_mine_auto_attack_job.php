<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Jobs\MineAutoAttackJob;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "=== ТЕСТИРОВАНИЕ MineAutoAttackJob ===\n";
    
    // Запускаем Job напрямую
    $job = new MineAutoAttackJob();
    
    echo "Запуск MineAutoAttackJob...\n";
    
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "✅ Job выполнен успешно\n";
    
} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
