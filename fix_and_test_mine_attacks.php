<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ИСПРАВЛЕНИЕ И ТЕСТ АВТОАТАК В РУДНИКАХ ===\n";

try {
    // 1. Очищаем старые данные
    echo "🧹 Очистка старых данных...\n";
    MineMark::where('player_id', 7)->delete();
    \App\Models\ActiveEffect::where('target_type', 'player')
        ->where('target_id', 7)
        ->where('effect_type', 'mine_detection')
        ->delete();

    // 2. Подготавливаем данные
    $user = User::find(7);
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";

    // 3. ИСПРАВЛЯЕМ ЛОКАЦИЮ ИГРОКА
    echo "\n🔧 Исправление локации игрока...\n";
    echo "   Текущая локация: '{$user->statistics->current_location}'\n";
    echo "   Устанавливаем локацию: '{$mineLocation->name}'\n";
    
    $user->statistics->current_location = $mineLocation->name;
    $user->statistics->save();
    
    echo "✅ Локация игрока обновлена\n";

    // 4. Создаем метку
    echo "\n🎯 Создание метки...\n";
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null,
        'attack_count' => 0,
    ]);
    
    echo "✅ Метка создана (ID: {$mark->id})\n";

    // 5. Создаем ActiveEffect для отображения
    $effect = \App\Models\ActiveEffect::create([
        'effect_type' => 'mine_detection',
        'effect_name' => 'Замечен в рудниках',
        'target_type' => 'player',
        'target_id' => $user->id,
        'duration' => 600,
        'starts_at' => now(),
        'ends_at' => $mark->expires_at,
        'is_active' => true,
        'effect_data' => [
            'mine_mark_id' => $mark->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'location_name' => $mineLocation->name,
            'description' => "Вы были замечены при добыче ресурсов в {$mineLocation->name}. Мобы могут атаковать вас!"
        ]
    ]);
    
    echo "✅ ActiveEffect создан (ID: {$effect->id})\n";

    // 6. Проверяем мобов
    echo "\n🐉 Проверка мобов в руднике...\n";
    $availableMobs = Mob::where('location_id', $mineLocation->location_id)
        ->where('mine_location_id', $mineLocation->id)
        ->where('hp', '>', 0)
        ->get();
    
    echo "   Доступных мобов: {$availableMobs->count()}\n";
    foreach ($availableMobs as $mob) {
        echo "     - {$mob->name} (ID: {$mob->id}) HP: {$mob->hp}/{$mob->max_hp}\n";
    }

    // 7. Проверяем HP игрока до атаки
    $playerHealthService = app(\App\Services\PlayerHealthService::class);
    $hpBefore = $playerHealthService->getCurrentHP($user);
    echo "\n💚 HP игрока до атаки: {$hpBefore}\n";

    // 8. Запускаем автоатаку
    echo "\n🚀 Запуск автоатаки мобов...\n";
    
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    
    echo "✅ MineAutoAttackJob выполнен\n";

    // 9. Проверяем результаты
    echo "\n📊 Проверка результатов...\n";
    
    // Обновляем метку из БД
    $markAfter = MineMark::find($mark->id);
    if ($markAfter) {
        echo "✅ Метка найдена после атаки\n";
        echo "   Последняя атака: " . ($markAfter->last_attack_at ? $markAfter->last_attack_at : 'НЕТ') . "\n";
        echo "   Количество атак: {$markAfter->attack_count}\n";
        
        if ($markAfter->last_attack_at) {
            echo "   🎯 АТАКА ПРОИЗОШЛА!\n";
        } else {
            echo "   ❌ Атака НЕ произошла\n";
        }
    } else {
        echo "❌ Метка не найдена после атаки (возможно удалена)\n";
    }
    
    // Проверяем HP игрока после атаки
    $hpAfter = $playerHealthService->getCurrentHP($user);
    echo "💚 HP игрока после атаки: {$hpAfter}\n";
    
    if ($hpBefore > $hpAfter) {
        echo "🎯 УРОН НАНЕСЕН! Потеряно HP: " . ($hpBefore - $hpAfter) . "\n";
    } else {
        echo "❌ Урон НЕ нанесен\n";
    }

    // 10. Проверяем журнал боя
    echo "\n📜 Проверка журнала боя...\n";
    $battleLogService = app(\App\Services\BattleLogService::class);
    $battleLogs = $battleLogService->getLogs("battle_logs:{$user->id}", null, 5);
    
    if (!empty($battleLogs)) {
        echo "✅ Найдено записей в журнале: " . count($battleLogs) . "\n";
        foreach ($battleLogs as $log) {
            if (is_array($log) && isset($log['message'])) {
                $message = strip_tags($log['message']);
                if (strlen($message) > 100) {
                    $message = substr($message, 0, 100) . '...';
                }
                echo "   - {$message}\n";
            }
        }
    } else {
        echo "❌ Записи в журнале боя не найдены\n";
    }

    // 11. Итоговый статус
    echo "\n=== ИТОГОВЫЙ СТАТУС ===\n";
    
    $success = false;
    if ($markAfter && $markAfter->last_attack_at) {
        echo "✅ СИСТЕМА АВТОАТАК РАБОТАЕТ!\n";
        echo "   - Метка создана корректно\n";
        echo "   - Игрок находится в правильной локации\n";
        echo "   - Мобы атакуют замеченных игроков\n";
        echo "   - Время последней атаки обновляется\n";
        $success = true;
    } else {
        echo "❌ СИСТЕМА АВТОАТАК НЕ РАБОТАЕТ\n";
        echo "   Возможные причины:\n";
        echo "   - Проблемы с логикой поиска мобов\n";
        echo "   - Проблемы с нанесением урона\n";
        echo "   - Проблемы с обновлением меток\n";
    }
    
    if ($hpBefore > $hpAfter) {
        echo "✅ УРОН НАНОСИТСЯ КОРРЕКТНО\n";
        $success = true;
    } else {
        echo "❌ УРОН НЕ НАНОСИТСЯ\n";
    }

    echo "\n" . ($success ? "🎉 ТЕСТ ПРОЙДЕН!" : "💥 ТЕСТ НЕ ПРОЙДЕН!") . "\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
