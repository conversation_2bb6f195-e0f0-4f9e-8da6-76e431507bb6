<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\MineLocation;
use App\Models\MineMark;
use App\Models\Mob;
use App\Services\MineDetectionService;

// Инициализация Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ДЕТАЛЬНАЯ ДИАГНОСТИКА АВТОАТАК В РУДНИКАХ ===\n";

try {
    // 1. Очищаем старые данные
    echo "🧹 Очистка старых данных...\n";
    MineMark::where('player_id', 7)->delete();
    \App\Models\ActiveEffect::where('target_type', 'player')
        ->where('target_id', 7)
        ->where('effect_type', 'mine_detection')
        ->delete();

    // 2. Подготавливаем данные
    $user = User::find(7);
    $mineLocation = MineLocation::where('is_active', true)->first();
    
    echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";
    echo "✅ Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})\n";
    echo "   Основная локация ID: {$mineLocation->location_id}\n";

    // 3. Создаем метку
    echo "\n🎯 Создание метки...\n";
    $mark = MineMark::create([
        'player_id' => $user->id,
        'mine_location_id' => $mineLocation->id,
        'location_id' => $mineLocation->location_id,
        'location_name' => $mineLocation->name,
        'expires_at' => now()->addMinutes(10),
        'is_active' => true,
        'last_attack_at' => null,
        'attack_count' => 0,
    ]);
    
    echo "✅ Метка создана (ID: {$mark->id})\n";
    echo "   Истекает: {$mark->expires_at}\n";
    echo "   Активна: " . ($mark->isActive() ? 'ДА' : 'НЕТ') . "\n";

    // 4. Проверяем данные игрока
    echo "\n👤 Проверка данных игрока...\n";
    echo "   ID: {$user->id}\n";
    echo "   Имя: {$user->name}\n";
    
    if ($user->profile) {
        echo "   Профиль найден: ДА\n";
        echo "   HP: {$user->profile->current_hp}/{$user->profile->max_hp}\n";
        echo "   Жив: " . ($user->profile->current_hp > 0 ? 'ДА' : 'НЕТ') . "\n";
    } else {
        echo "   Профиль найден: НЕТ\n";
    }
    
    if ($user->statistics) {
        echo "   Статистика найдена: ДА\n";
        echo "   Текущая локация: '{$user->statistics->current_location}'\n";
        echo "   Ожидаемая локация: '{$mineLocation->name}'\n";
        echo "   Локации совпадают: " . ($user->statistics->current_location === $mineLocation->name ? 'ДА' : 'НЕТ') . "\n";
    } else {
        echo "   Статистика найдена: НЕТ\n";
    }

    // 5. Симулируем getActiveMarkedPlayers
    echo "\n🔍 Симуляция getActiveMarkedPlayers...\n";
    
    $marks = MineMark::with(['player.profile', 'mineLocation'])
        ->where('is_active', true)
        ->where('expires_at', '>', now())
        ->get();
    
    echo "   Найдено меток в БД: {$marks->count()}\n";
    
    $validPlayers = [];
    foreach ($marks as $mark) {
        echo "\n   Проверка метки ID: {$mark->id}\n";
        
        // Проверяем игрока
        if (!$mark->player) {
            echo "     ❌ Игрок не найден\n";
            continue;
        }
        echo "     ✅ Игрок найден: {$mark->player->name}\n";
        
        // Проверяем профиль
        if (!$mark->player->profile) {
            echo "     ❌ Профиль игрока не найден\n";
            continue;
        }
        echo "     ✅ Профиль найден\n";
        
        // Проверяем HP
        if ($mark->player->profile->current_hp <= 0) {
            echo "     ❌ Игрок мертв (HP: {$mark->player->profile->current_hp})\n";
            continue;
        }
        echo "     ✅ Игрок жив (HP: {$mark->player->profile->current_hp})\n";
        
        // Проверяем локацию рудника
        if (!$mark->mineLocation) {
            echo "     ❌ Локация рудника не найдена\n";
            continue;
        }
        echo "     ✅ Локация рудника найдена: {$mark->mineLocation->name}\n";
        
        // Проверяем статистику игрока
        if (!$mark->player->statistics) {
            echo "     ❌ Статистика игрока не найдена\n";
            continue;
        }
        echo "     ✅ Статистика найдена\n";
        
        // Проверяем текущую локацию игрока
        $playerLocation = $mark->player->statistics->current_location;
        $expectedLocation = $mark->location_name;
        
        echo "     Текущая локация игрока: '{$playerLocation}'\n";
        echo "     Ожидаемая локация: '{$expectedLocation}'\n";
        
        if ($playerLocation !== $expectedLocation) {
            echo "     ❌ Игрок не в той локации\n";
            continue;
        }
        echo "     ✅ Игрок в правильной локации\n";
        
        // Если все проверки прошли
        $validPlayers[] = [
            'mark_id' => $mark->id,
            'player_id' => $mark->player_id,
            'player' => $mark->player,
            'mine_location_id' => $mark->mine_location_id,
            'mine_location' => $mark->mineLocation,
            'location_id' => $mark->location_id,
            'location_name' => $mark->location_name,
            'expires_at' => $mark->expires_at,
            'last_attack_at' => $mark->last_attack_at,
            'attack_count' => $mark->attack_count ?? 0
        ];
        
        echo "     ✅ Игрок прошел ВСЕ проверки!\n";
    }
    
    echo "\n   Валидных игроков для атаки: " . count($validPlayers) . "\n";

    // 6. Если есть валидные игроки, проверяем мобов
    if (count($validPlayers) > 0) {
        echo "\n🐉 Проверка мобов...\n";
        
        $player = $validPlayers[0];
        $mineLocationObj = $player['mine_location'];
        
        $availableMobs = Mob::where('location_id', $mineLocationObj->location_id)
            ->where('mine_location_id', $mineLocationObj->id)
            ->where('hp', '>', 0)
            ->get();
        
        echo "   Доступных мобов в руднике: {$availableMobs->count()}\n";
        
        foreach ($availableMobs as $mob) {
            echo "     - {$mob->name} (ID: {$mob->id}) HP: {$mob->hp}/{$mob->max_hp}\n";
        }
        
        if ($availableMobs->count() > 0) {
            echo "\n🎯 ВСЕ УСЛОВИЯ ДЛЯ АТАКИ ВЫПОЛНЕНЫ!\n";
            echo "   - Есть валидные игроки: " . count($validPlayers) . "\n";
            echo "   - Есть доступные мобы: {$availableMobs->count()}\n";
            echo "   - Автоатака должна сработать!\n";
        } else {
            echo "\n❌ Нет доступных мобов для атаки\n";
        }
    } else {
        echo "\n❌ Нет валидных игроков для атаки\n";
    }

    echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";

} catch (\Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
